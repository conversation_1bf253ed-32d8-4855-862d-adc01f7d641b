/**
 * NFT模块
 */

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NFTService } from './nft.service';
import { NFTController } from './nft.controller';
import { NFTToken } from '../../entities/nft-token.entity';
import { BlockchainAsset } from '../../entities/blockchain-asset.entity';

@Module({
  imports: [TypeOrmModule.forFeature([NFTToken, BlockchainAsset])],
  controllers: [NFTController],
  providers: [NFTService],
  exports: [NFTService],
})
export class NFTModule {}
