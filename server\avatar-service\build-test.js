/**
 * 构建测试脚本
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 开始诊断构建问题...');

// 检查文件是否存在
const filesToCheck = [
  'src/app.module.ts',
  'src/main.ts',
  'src/avatar/avatar.module.ts',
  'src/avatar/entities/avatar.entity.ts'
];

console.log('\n📁 检查文件存在性:');
filesToCheck.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`${exists ? '✅' : '❌'} ${file}`);
});

// 检查 TypeScript 配置
console.log('\n⚙️ 检查 TypeScript 配置:');
try {
  const tsconfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
  console.log('✅ tsconfig.json 解析成功');
  console.log('   baseUrl:', tsconfig.compilerOptions.baseUrl);
  console.log('   moduleResolution:', tsconfig.compilerOptions.moduleResolution);
} catch (error) {
  console.log('❌ tsconfig.json 解析失败:', error.message);
}

// 尝试编译单个文件
console.log('\n🔨 尝试编译单个文件:');
try {
  console.log('编译 app.module.ts...');
  execSync('npx tsc src/app.module.ts --noEmit --skipLibCheck', { 
    encoding: 'utf8',
    timeout: 10000 
  });
  console.log('✅ app.module.ts 编译成功');
} catch (error) {
  console.log('❌ app.module.ts 编译失败:');
  console.log(error.stdout || error.message);
}

try {
  console.log('编译 main.ts...');
  execSync('npx tsc src/main.ts --noEmit --skipLibCheck', { 
    encoding: 'utf8',
    timeout: 10000 
  });
  console.log('✅ main.ts 编译成功');
} catch (error) {
  console.log('❌ main.ts 编译失败:');
  console.log(error.stdout || error.message);
}

console.log('\n🎯 诊断完成！');
