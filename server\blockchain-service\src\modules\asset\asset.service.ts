/**
 * 资产服务
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BlockchainAsset } from '../../entities/blockchain-asset.entity';
import { AssetMetadata } from '../../entities/asset-metadata.entity';

@Injectable()
export class AssetService {
  private readonly logger = new Logger(AssetService.name);

  constructor(
    @InjectRepository(BlockchainAsset)
    private assetRepository: Repository<BlockchainAsset>,
    @InjectRepository(AssetMetadata)
    private metadataRepository: Repository<AssetMetadata>,
  ) {}

  /**
   * 创建资产
   */
  async createAsset(assetData: Partial<BlockchainAsset>) {
    const asset = this.assetRepository.create(assetData);
    return this.assetRepository.save(asset);
  }

  /**
   * 获取资产详情
   */
  async getAssetById(id: string) {
    return this.assetRepository.findOne({
      where: { id },
      relations: ['owner', 'creator', 'contract', 'metadata', 'listings'],
    });
  }

  /**
   * 根据合约地址和tokenId获取资产
   */
  async getAssetByToken(contractAddress: string, tokenId: string) {
    return this.assetRepository.findOne({
      where: { contractAddress, tokenId },
      relations: ['owner', 'creator', 'contract', 'metadata'],
    });
  }

  /**
   * 获取用户拥有的资产
   */
  async getAssetsByOwner(ownerId: string) {
    return this.assetRepository.find({
      where: { ownerId },
      relations: ['contract', 'metadata'],
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 更新资产信息
   */
  async updateAsset(id: string, updateData: Partial<BlockchainAsset>) {
    await this.assetRepository.update(id, updateData);
    return this.getAssetById(id);
  }
}
