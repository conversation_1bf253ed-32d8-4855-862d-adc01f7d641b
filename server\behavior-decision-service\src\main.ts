/**
 * 分布式行为决策服务启动文件
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // 启用全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));
  
  // 启用CORS
  app.enableCors({
    origin: true,
    credentials: true,
  });
  
  const configService = app.get(ConfigService);
  const port = configService.get('PORT', 3008);
  
  await app.listen(port);
  console.log(`分布式行为决策服务已启动，端口: ${port}`);
}

bootstrap().catch(error => {
  console.error('服务启动失败:', error);
  process.exit(1);
});
