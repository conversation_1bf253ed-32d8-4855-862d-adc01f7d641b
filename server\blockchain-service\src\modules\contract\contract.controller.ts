/**
 * 智能合约控制器
 */

import { Controller, Get, Post, Put, Param, Body } from '@nestjs/common';
import { ContractService } from './contract.service';
import { SmartContract } from '../../entities/smart-contract.entity';

@Controller('contracts')
export class ContractController {
  constructor(private readonly contractService: ContractService) {}

  @Post()
  async createContract(@Body() contractData: Partial<SmartContract>) {
    return this.contractService.createContract(contractData);
  }

  @Get()
  async getAllContracts() {
    return this.contractService.getAllContracts();
  }

  @Get(':address')
  async getContract(@Param('address') address: string) {
    return this.contractService.getContractByAddress(address);
  }

  @Put(':address')
  async updateContract(
    @Param('address') address: string,
    @Body() updateData: Partial<SmartContract>
  ) {
    return this.contractService.updateContract(address, updateData);
  }
}
