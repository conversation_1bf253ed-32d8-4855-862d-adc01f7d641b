/**
 * 区块链资产实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { SmartContract } from './smart-contract.entity';
import { Transaction } from './transaction.entity';
import { MarketplaceListing } from './marketplace-listing.entity';
import { AssetMetadata } from './asset-metadata.entity';

@Entity('blockchain_assets')
@Index(['tokenId', 'contractAddress'], { unique: true })
@Index(['contractAddress'])
@Index(['ownerId'])
@Index(['creatorId'])
export class BlockchainAsset {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  tokenId: string;

  @Column({ type: 'varchar', length: 42, nullable: false })
  contractAddress: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  image: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  animationUrl: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  externalUrl: string;

  @Column({ type: 'enum', enum: ['ERC721', 'ERC1155'], nullable: false })
  tokenStandard: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  blockchain: string;

  @Column({ type: 'bigint', default: 1 })
  supply: number;

  @Column({ type: 'bigint', default: 1 })
  currentSupply: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, nullable: true })
  royaltyPercentage: number;

  @Column({ type: 'varchar', length: 42, nullable: true })
  royaltyRecipient: string;

  @Column({ type: 'json', nullable: true })
  attributes: Record<string, any>[];

  @Column({ type: 'json', nullable: true })
  properties: Record<string, any>;

  @Column({ type: 'varchar', length: 100, nullable: true })
  category: string;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @Column({ type: 'boolean', default: false })
  isListed: boolean;

  @Column({ type: 'boolean', default: false })
  isVerified: boolean;

  @Column({ type: 'enum', enum: ['draft', 'minting', 'minted', 'burned'], default: 'draft' })
  status: string;

  @Column({ type: 'varchar', length: 66, nullable: true })
  mintTransactionHash: string;

  @Column({ type: 'timestamp', nullable: true })
  mintedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, user => user.ownedAssets)
  @JoinColumn({ name: 'ownerId' })
  owner: User;

  @Column({ type: 'uuid' })
  ownerId: string;

  @ManyToOne(() => User, user => user.createdAssets)
  @JoinColumn({ name: 'creatorId' })
  creator: User;

  @Column({ type: 'uuid' })
  creatorId: string;

  @ManyToOne(() => SmartContract, contract => contract.assets)
  @JoinColumn({ name: 'contractAddress', referencedColumnName: 'address' })
  contract: SmartContract;

  @OneToMany(() => Transaction, transaction => transaction.asset)
  transactions: Transaction[];

  @OneToMany(() => MarketplaceListing, listing => listing.asset)
  listings: MarketplaceListing[];

  @OneToMany(() => AssetMetadata, metadata => metadata.asset)
  metadata: AssetMetadata[];
}
