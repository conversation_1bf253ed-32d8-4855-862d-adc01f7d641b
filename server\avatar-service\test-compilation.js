/**
 * 简单的编译测试脚本
 * 用于验证 TypeScript 编译是否成功
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('开始 TypeScript 编译测试...');

try {
  // 切换到项目目录
  process.chdir(path.join(__dirname));
  
  // 运行 TypeScript 编译检查
  console.log('运行 TypeScript 编译检查...');
  const result = execSync('npx tsc --noEmit --skipLibCheck', { 
    encoding: 'utf8',
    timeout: 30000 
  });
  
  console.log('✅ TypeScript 编译检查通过！');
  console.log('输出:', result);
  
} catch (error) {
  console.error('❌ TypeScript 编译检查失败:');
  console.error(error.stdout || error.message);
  process.exit(1);
}

console.log('🎉 所有错误修复验证完成！');
