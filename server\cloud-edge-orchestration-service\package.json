{"name": "cloud-edge-orchestration-service", "version": "1.0.0", "description": "云边协同编排服务 - 混合云部署、边缘计算网络、5G网络应用", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@azure/arm-resources": "^5.2.0", "@google-cloud/compute": "^4.1.0", "@kubernetes/client-node": "^0.20.0", "@nestjs/common": "^9.4.3", "@nestjs/config": "^2.3.4", "@nestjs/core": "^9.4.3", "@nestjs/event-emitter": "^2.0.0", "@nestjs/platform-express": "^9.4.3", "@nestjs/platform-socket.io": "^9.4.3", "@nestjs/schedule": "^2.2.3", "@nestjs/swagger": "^6.3.0", "@nestjs/typeorm": "^9.0.1", "@nestjs/websockets": "^9.4.3", "@types/ioredis": "^4.28.10", "aws-sdk": "^2.1467.0", "axios": "^1.5.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "dockerode": "^4.0.2", "express": "^4.18.2", "ioredis": "^5.6.1", "lodash": "^4.17.21", "moment": "^2.29.4", "mysql2": "^3.6.0", "redis": "^4.6.7", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.2", "typeorm": "^0.3.17", "uuid": "^9.0.0", "ws": "^8.14.2"}, "devDependencies": {"@nestjs/cli": "^9.5.0", "@nestjs/schematics": "^9.2.0", "@nestjs/testing": "^9.4.3", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.14.195", "@types/node": "^20.3.1", "@types/uuid": "^9.0.2", "@types/ws": "^8.5.5", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}