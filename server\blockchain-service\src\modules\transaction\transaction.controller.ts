/**
 * 交易控制器
 */

import { Controller, Get, Post, Put, Param, Body } from '@nestjs/common';
import { TransactionService } from './transaction.service';
import { Transaction } from '../../entities/transaction.entity';

@Controller('transactions')
export class TransactionController {
  constructor(private readonly transactionService: TransactionService) {}

  @Post()
  async createTransaction(@Body() transactionData: Partial<Transaction>) {
    return this.transactionService.createTransaction(transactionData);
  }

  @Get(':id')
  async getTransaction(@Param('id') id: string) {
    return this.transactionService.getTransactionById(id);
  }

  @Get('hash/:hash')
  async getTransactionByHash(@Param('hash') hash: string) {
    return this.transactionService.getTransactionByHash(hash);
  }

  @Get('user/:userId')
  async getUserTransactions(@Param('userId') userId: string) {
    return this.transactionService.getUserTransactions(userId);
  }

  @Put('hash/:hash/status')
  async updateTransactionStatus(
    @Param('hash') hash: string,
    @Body() { status, confirmations }: { status: string; confirmations?: number }
  ) {
    return this.transactionService.updateTransactionStatus(hash, status, confirmations);
  }
}
