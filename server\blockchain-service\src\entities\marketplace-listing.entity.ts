/**
 * 市场列表实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { BlockchainAsset } from './blockchain-asset.entity';

@Entity('marketplace_listings')
@Index(['sellerId'])
@Index(['assetId'])
@Index(['status'])
@Index(['blockchain'])
export class MarketplaceListing {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'decimal', precision: 36, scale: 18, nullable: false })
  price: string;

  @Column({ type: 'varchar', length: 42, nullable: true })
  paymentToken: string;

  @Column({ type: 'varchar', length: 10, default: 'ETH' })
  currency: string;

  @Column({ type: 'enum', enum: ['fixed', 'auction', 'offer'], default: 'fixed' })
  listingType: string;

  @Column({ type: 'enum', enum: ['active', 'sold', 'cancelled', 'expired'], default: 'active' })
  status: string;

  @Column({ type: 'bigint', default: 1 })
  quantity: number;

  @Column({ type: 'bigint', default: 1 })
  remainingQuantity: number;

  @Column({ type: 'timestamp', nullable: true })
  startTime: Date;

  @Column({ type: 'timestamp', nullable: true })
  endTime: Date;

  @Column({ type: 'decimal', precision: 36, scale: 18, nullable: true })
  reservePrice: string;

  @Column({ type: 'decimal', precision: 36, scale: 18, nullable: true })
  buyNowPrice: string;

  @Column({ type: 'decimal', precision: 36, scale: 18, nullable: true })
  highestBid: string;

  @Column({ type: 'uuid', nullable: true })
  highestBidderId: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  blockchain: string;

  @Column({ type: 'varchar', length: 66, nullable: true })
  listingTxHash: string;

  @Column({ type: 'varchar', length: 66, nullable: true })
  saleTxHash: string;

  @Column({ type: 'varchar', length: 66, nullable: true })
  cancelTxHash: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'timestamp', nullable: true })
  soldAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, user => user.listings)
  @JoinColumn({ name: 'sellerId' })
  seller: User;

  @Column({ type: 'uuid' })
  sellerId: string;

  @ManyToOne(() => BlockchainAsset, asset => asset.listings)
  @JoinColumn({ name: 'assetId' })
  asset: BlockchainAsset;

  @Column({ type: 'uuid' })
  assetId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'buyerId' })
  buyer: User;

  @Column({ type: 'uuid', nullable: true })
  buyerId: string;
}
