/**
 * 生产数据实体
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';

@Entity('production_data')
export class ProductionDataEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn()
  timestamp: Date;

  @Column({ length: 50 })
  deviceId: string;

  @Column({ length: 50 })
  deviceType: string;

  @Column({ length: 50 })
  productionLine: string;

  @Column('decimal', { precision: 10, scale: 2 })
  output: number;

  @Column('decimal', { precision: 5, scale: 2 })
  quality: number;

  @Column('decimal', { precision: 5, scale: 2 })
  efficiency: number;

  @Column('decimal', { precision: 8, scale: 2 })
  downtime: number;

  @Column('decimal', { precision: 10, scale: 2 })
  energyConsumption: number;

  @Column('decimal', { precision: 5, scale: 2 })
  temperature: number;

  @Column('decimal', { precision: 8, scale: 2 })
  pressure: number;

  @Column('decimal', { precision: 5, scale: 2 })
  vibration: number;
}
