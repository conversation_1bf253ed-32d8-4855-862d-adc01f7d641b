/**
 * 区块链服务应用模块
 */

import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { JwtModule } from '@nestjs/jwt';

// 模块导入
import { BlockchainModule } from './modules/blockchain/blockchain.module';
import { NFTModule } from './modules/nft/nft.module';
import { ContractModule } from './modules/contract/contract.module';
import { AssetModule } from './modules/asset/asset.module';
import { MarketplaceModule } from './modules/marketplace/marketplace.module';
import { TransactionModule } from './modules/transaction/transaction.module';
import { IPFSModule } from './modules/ipfs/ipfs.module';
import { AuthModule } from './modules/auth/auth.module';

// 实体导入
import { User } from './entities/user.entity';
import { BlockchainAsset } from './entities/blockchain-asset.entity';
import { NFTToken } from './entities/nft-token.entity';
import { SmartContract } from './entities/smart-contract.entity';
import { Transaction } from './entities/transaction.entity';
import { MarketplaceListing } from './entities/marketplace-listing.entity';
import { AssetMetadata } from './entities/asset-metadata.entity';

// 配置
import databaseConfig from './config/database.config';
import redisConfig from './config/redis.config';
import blockchainConfig from './config/blockchain.config';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, redisConfig, blockchainConfig],
      envFilePath: ['.env.local', '.env'],
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('database.host'),
        port: configService.get('database.port'),
        username: configService.get('database.username'),
        password: configService.get('database.password'),
        database: configService.get('database.database'),
        entities: [
          User,
          BlockchainAsset,
          NFTToken,
          SmartContract,
          Transaction,
          MarketplaceListing,
          AssetMetadata,
        ],
        synchronize: configService.get('NODE_ENV') !== 'production',
        logging: configService.get('NODE_ENV') === 'development',
        timezone: '+08:00',
        charset: 'utf8mb4',
        extra: {
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
        },
      }),
      inject: [ConfigService],
    }),

    // Redis/Bull队列模块
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get('redis.host'),
          port: configService.get('redis.port'),
          password: configService.get('redis.password'),
          db: configService.get('redis.db'),
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }),
      inject: [ConfigService],
    }),

    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET', 'blockchain-service-secret'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN', '24h'),
        },
      }),
      inject: [ConfigService],
    }),

    // 业务模块
    AuthModule,
    BlockchainModule,
    NFTModule,
    ContractModule,
    AssetModule,
    MarketplaceModule,
    TransactionModule,
    IPFSModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
