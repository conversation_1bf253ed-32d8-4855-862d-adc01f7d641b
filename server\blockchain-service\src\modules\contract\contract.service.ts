/**
 * 智能合约服务
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SmartContract } from '../../entities/smart-contract.entity';

@Injectable()
export class ContractService {
  private readonly logger = new Logger(ContractService.name);

  constructor(
    @InjectRepository(SmartContract)
    private smartContractRepository: Repository<SmartContract>,
  ) {}

  /**
   * 创建智能合约
   */
  async createContract(contractData: Partial<SmartContract>) {
    const contract = this.smartContractRepository.create(contractData);
    return this.smartContractRepository.save(contract);
  }

  /**
   * 根据地址获取合约
   */
  async getContractByAddress(address: string) {
    return this.smartContractRepository.findOne({
      where: { address },
      relations: ['assets'],
    });
  }

  /**
   * 获取所有合约
   */
  async getAllContracts() {
    return this.smartContractRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 更新合约信息
   */
  async updateContract(address: string, updateData: Partial<SmartContract>) {
    await this.smartContractRepository.update({ address }, updateData);
    return this.getContractByAddress(address);
  }
}
