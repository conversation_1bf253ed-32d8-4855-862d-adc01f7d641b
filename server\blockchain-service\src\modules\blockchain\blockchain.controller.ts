/**
 * 区块链控制器
 */

import { Controller, Get, Param, Query } from '@nestjs/common';
import { BlockchainService } from './blockchain.service';

@Controller('blockchain')
export class BlockchainController {
  constructor(private readonly blockchainService: BlockchainService) {}

  @Get('networks/:blockchain')
  async getNetworkInfo(@Param('blockchain') blockchain: string) {
    return this.blockchainService.getNetworkInfo(blockchain);
  }

  @Get('transactions/:hash')
  async getTransaction(@Param('hash') hash: string) {
    return this.blockchainService.getTransaction(hash);
  }
}
