/**
 * NFT服务 - 处理NFT相关的业务逻辑
 */

import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { NFTToken } from '../../entities/nft-token.entity';
import { BlockchainAsset } from '../../entities/blockchain-asset.entity';

@Injectable()
export class NFTService {
  private readonly logger = new Logger(NFTService.name);

  constructor(
    @InjectRepository(NFTToken)
    private readonly nftRepository: Repository<NFTToken>,
    @InjectRepository(BlockchainAsset)
    private readonly assetRepository: Repository<BlockchainAsset>,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 创建NFT
   */
  async createNFT(nftData: Partial<NFTToken>): Promise<NFTToken> {
    this.logger.log(`创建NFT`);

    try {
      const nft = this.nftRepository.create(nftData);
      const savedNFT = await this.nftRepository.save(nft);

      this.logger.log(`NFT创建成功: ${savedNFT.id}`);
      return savedNFT;
    } catch (error) {
      this.logger.error(`NFT创建失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 铸造NFT
   */
  async mintNFT(id: string, mintData: any): Promise<NFTToken> {
    this.logger.log(`铸造NFT: ${id}`);

    try {
      const nft = await this.getNFTById(id);

      // 更新NFT状态
      nft.status = 'active';

      if (mintData.transactionHash) {
        nft.mintTransactionHash = mintData.transactionHash;
      }

      const updatedNFT = await this.nftRepository.save(nft);

      this.logger.log(`NFT铸造成功: ${updatedNFT.id}`);
      return updatedNFT;
    } catch (error) {
      this.logger.error(`NFT铸造失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 转移NFT
   */
  async transferNFT(id: string, to: string, amount?: number): Promise<NFTToken> {
    this.logger.log(`转移NFT: ${id} -> ${to}`);

    try {
      const nft = await this.getNFTById(id);

      // 简单的转移逻辑
      // 在实际应用中，这里应该调用区块链服务
      this.logger.log(`NFT转移成功: ${nft.id}`);
      return nft;
    } catch (error) {
      this.logger.error(`NFT转移失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据ID获取NFT
   */
  async getNFTById(id: string): Promise<NFTToken> {
    const nft = await this.nftRepository.findOne({
      where: { id },
    });

    if (!nft) {
      throw new NotFoundException('NFT不存在');
    }

    return nft;
  }

  /**
   * 根据Token ID和合约地址获取NFT
   */
  async getNFTByToken(contractAddress: string, tokenId: string): Promise<NFTToken> {
    const nft = await this.nftRepository.findOne({
      where: { contractAddress, tokenId },
    });

    if (!nft) {
      throw new NotFoundException('NFT不存在');
    }

    return nft;
  }

  /**
   * 获取用户拥有的NFT
   */
  async getNFTsByOwner(ownerAddress: string): Promise<NFTToken[]> {
    return this.nftRepository.find({
      where: { ownerAddress },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 更新NFT
   */
  async updateNFT(id: string, updateData: Partial<NFTToken>): Promise<NFTToken> {
    await this.nftRepository.update(id, updateData);
    return this.getNFTById(id);
  }

}
