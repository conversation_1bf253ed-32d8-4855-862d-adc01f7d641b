/**
 * 市场控制器
 */

import { Controller, Get, Post, Put, Param, Body } from '@nestjs/common';
import { MarketplaceService } from './marketplace.service';
import { MarketplaceListing } from '../../entities/marketplace-listing.entity';

@Controller('marketplace')
export class MarketplaceController {
  constructor(private readonly marketplaceService: MarketplaceService) {}

  @Post('listings')
  async createListing(@Body() listingData: Partial<MarketplaceListing>) {
    return this.marketplaceService.createListing(listingData);
  }

  @Get('listings')
  async getActiveListings() {
    return this.marketplaceService.getActiveListings();
  }

  @Get('listings/:id')
  async getListing(@Param('id') id: string) {
    return this.marketplaceService.getListingById(id);
  }

  @Get('seller/:sellerId/listings')
  async getListingsBySeller(@Param('sellerId') sellerId: string) {
    return this.marketplaceService.getListingsBySeller(sellerId);
  }

  @Put('listings/:id/status')
  async updateListingStatus(
    @Param('id') id: string,
    @Body() { status, buyerId }: { status: string; buyerId?: string }
  ) {
    return this.marketplaceService.updateListingStatus(id, status, buyerId);
  }
}
