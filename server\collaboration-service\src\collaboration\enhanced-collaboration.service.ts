/**
 * 增强协作服务
 * 
 * 提供高级协作功能，包括：
 * - 智能冲突检测和解决
 * - 细粒度权限控制
 * - 实时同步优化
 * - 操作转换算法
 * - 协作性能监控
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import Redis from 'ioredis';
import { v4 as uuidv4 } from 'uuid';

/**
 * 权限级别枚举
 */
export enum PermissionLevel {
  NONE = 0,
  READ = 1,
  COMMENT = 2,
  EDIT = 3,
  ADMIN = 4,
  OWNER = 5
}

/**
 * 操作类型枚举
 */
export enum OperationType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  MOVE = 'move',
  COPY = 'copy',
  STYLE = 'style',
  PROPERTY = 'property'
}

/**
 * 冲突类型枚举
 */
export enum ConflictType {
  CONCURRENT_EDIT = 'concurrent_edit',
  DEPENDENCY_VIOLATION = 'dependency_violation',
  PERMISSION_CONFLICT = 'permission_conflict',
  RESOURCE_LOCK = 'resource_lock',
  VERSION_MISMATCH = 'version_mismatch'
}

/**
 * 操作接口
 */
export interface Operation {
  id: string;
  type: OperationType;
  userId: string;
  timestamp: number;
  targetId: string;
  targetType: string;
  data: any;
  dependencies?: string[];
  version: number;
  sessionId: string;
}

/**
 * 冲突接口
 */
export interface Conflict {
  id: string;
  type: ConflictType;
  operations: Operation[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  autoResolvable: boolean;
  suggestedResolution?: any;
  affectedUsers: string[];
  timestamp: number;
}

/**
 * 权限规则接口
 */
export interface PermissionRule {
  resourceType: string;
  resourceId?: string;
  userId?: string;
  userRole?: string;
  permission: PermissionLevel;
  conditions?: any;
  expiresAt?: Date;
}

/**
 * 协作会话接口
 */
export interface CollaborationSession {
  sessionId: string;
  userId: string;
  projectId: string;
  sceneId: string;
  permissions: PermissionLevel;
  isActive: boolean;
  lastActivity: Date;
  operationCount: number;
  conflictCount: number;
}

/**
 * 增强协作服务
 */
@Injectable()
export class EnhancedCollaborationService {
  private readonly logger = new Logger(EnhancedCollaborationService.name);
  private readonly redis: Redis;
  
  // 协作状态管理
  private activeSessions = new Map<string, CollaborationSession>();
  private operationHistory = new Map<string, Operation[]>();
  private conflictQueue = new Map<string, Conflict[]>();
  private permissionRules = new Map<string, PermissionRule[]>();
  
  // 性能监控
  private performanceMetrics = {
    totalOperations: 0,
    conflictsDetected: 0,
    conflictsResolved: 0,
    averageResolutionTime: 0,
    activeUsers: 0,
    throughput: 0
  };
  
  // 配置参数
  private readonly maxOperationHistory = 10000;
  private readonly conflictDetectionInterval = 1000; // 1秒
  private readonly sessionTimeoutMs = 300000; // 5分钟
  private readonly maxConcurrentOperations = 100;

  constructor(
    private readonly eventEmitter: EventEmitter2
  ) {
    // 使用默认Redis配置
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
    });
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 加载权限规则
      await this.loadPermissionRules();
      
      // 启动冲突检测
      this.startConflictDetection();
      
      // 启动性能监控
      this.startPerformanceMonitoring();
      
      this.logger.log('增强协作服务已启动');
      
    } catch (error) {
      this.logger.error('服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建协作会话
   */
  public async createSession(
    userId: string,
    projectId: string,
    sceneId: string,
    userRole?: string
  ): Promise<string> {
    try {
      const sessionId = uuidv4();
      
      // 检查用户权限
      const permissions = await this.getUserPermissions(userId, projectId, sceneId, userRole);
      
      if (permissions === PermissionLevel.NONE) {
        throw new Error('用户没有访问权限');
      }
      
      const session: CollaborationSession = {
        sessionId,
        userId,
        projectId,
        sceneId,
        permissions,
        isActive: true,
        lastActivity: new Date(),
        operationCount: 0,
        conflictCount: 0
      };
      
      // 存储会话
      this.activeSessions.set(sessionId, session);
      
      // 保存到Redis
      await this.redis.setex(
        `collaboration:session:${sessionId}`,
        this.sessionTimeoutMs / 1000,
        JSON.stringify(session)
      );
      
      // 更新性能指标
      this.performanceMetrics.activeUsers = this.activeSessions.size;
      
      this.eventEmitter.emit('collaboration.session.created', session);
      this.logger.log(`协作会话已创建: ${sessionId}`);
      
      return sessionId;
      
    } catch (error) {
      this.logger.error('创建协作会话失败:', error);
      throw error;
    }
  }

  /**
   * 提交操作
   */
  public async submitOperation(
    sessionId: string,
    operation: Omit<Operation, 'id' | 'timestamp' | 'version' | 'sessionId'>
  ): Promise<{
    success: boolean;
    operationId?: string;
    conflicts?: Conflict[];
    transformedOperation?: Operation;
  }> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session || !session.isActive) {
        throw new Error('无效的会话');
      }
      
      // 检查操作权限
      const hasPermission = await this.checkOperationPermission(session, operation);
      if (!hasPermission) {
        throw new Error('没有执行此操作的权限');
      }
      
      // 创建完整的操作对象
      const fullOperation: Operation = {
        ...operation,
        id: uuidv4(),
        timestamp: Date.now(),
        version: await this.getNextVersion(operation.targetId),
        sessionId
      };
      
      // 检测冲突
      const conflicts = await this.detectConflicts(fullOperation);
      
      if (conflicts.length > 0) {
        // 尝试自动解决冲突
        const resolutionResult = await this.resolveConflicts(conflicts, fullOperation);
        
        if (resolutionResult.success) {
          // 应用转换后的操作
          await this.applyOperation(resolutionResult.transformedOperation!);
          
          return {
            success: true,
            operationId: resolutionResult.transformedOperation!.id,
            conflicts,
            transformedOperation: resolutionResult.transformedOperation
          };
        } else {
          // 冲突无法自动解决，加入冲突队列
          await this.addToConflictQueue(conflicts);
          
          return {
            success: false,
            conflicts
          };
        }
      } else {
        // 没有冲突，直接应用操作
        await this.applyOperation(fullOperation);
        
        return {
          success: true,
          operationId: fullOperation.id
        };
      }
      
    } catch (error) {
      this.logger.error('提交操作失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户权限
   */
  private async getUserPermissions(
    userId: string,
    projectId: string,
    sceneId: string,
    userRole?: string
  ): Promise<PermissionLevel> {
    try {
      // 检查项目级权限
      const projectRules = this.permissionRules.get(`project:${projectId}`) || [];
      
      // 检查场景级权限
      const sceneRules = this.permissionRules.get(`scene:${sceneId}`) || [];
      
      // 合并权限规则
      const allRules = [...projectRules, ...sceneRules];
      
      let maxPermission = PermissionLevel.NONE;
      
      for (const rule of allRules) {
        if (this.matchesRule(rule, userId, userRole)) {
          maxPermission = Math.max(maxPermission, rule.permission);
        }
      }
      
      return maxPermission;
      
    } catch (error) {
      this.logger.error('获取用户权限失败:', error);
      return PermissionLevel.NONE;
    }
  }

  /**
   * 检查权限规则匹配
   */
  private matchesRule(rule: PermissionRule, userId: string, userRole?: string): boolean {
    // 检查用户ID匹配
    if (rule.userId && rule.userId !== userId) {
      return false;
    }
    
    // 检查用户角色匹配
    if (rule.userRole && rule.userRole !== userRole) {
      return false;
    }
    
    // 检查过期时间
    if (rule.expiresAt && new Date() > rule.expiresAt) {
      return false;
    }
    
    // 检查其他条件
    if (rule.conditions) {
      // 这里可以实现更复杂的条件检查逻辑
      return this.evaluateConditions(rule.conditions, userId, userRole);
    }
    
    return true;
  }

  /**
   * 评估权限条件
   */
  private evaluateConditions(conditions: any, userId: string, userRole?: string): boolean {
    // 简化的条件评估实现
    // 实际应用中可以实现更复杂的条件逻辑
    
    if (conditions.timeRange) {
      const now = new Date();
      const start = new Date(conditions.timeRange.start);
      const end = new Date(conditions.timeRange.end);
      
      if (now < start || now > end) {
        return false;
      }
    }
    
    if (conditions.maxConcurrentUsers) {
      if (this.activeSessions.size >= conditions.maxConcurrentUsers) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * 检查操作权限
   */
  private async checkOperationPermission(
    session: CollaborationSession,
    operation: Partial<Operation>
  ): Promise<boolean> {
    const requiredPermission = this.getRequiredPermissionForOperation(operation.type!);
    return session.permissions >= requiredPermission;
  }

  /**
   * 获取操作所需权限级别
   */
  private getRequiredPermissionForOperation(operationType: OperationType): PermissionLevel {
    switch (operationType) {
      case OperationType.CREATE:
      case OperationType.UPDATE:
      case OperationType.DELETE:
      case OperationType.MOVE:
        return PermissionLevel.EDIT;
      
      case OperationType.COPY:
      case OperationType.STYLE:
      case OperationType.PROPERTY:
        return PermissionLevel.EDIT;
      
      default:
        return PermissionLevel.READ;
    }
  }

  /**
   * 获取下一个版本号
   */
  private async getNextVersion(targetId: string): Promise<number> {
    const versionKey = `collaboration:version:${targetId}`;
    const currentVersion = await this.redis.get(versionKey);
    const nextVersion = currentVersion ? parseInt(currentVersion) + 1 : 1;
    
    await this.redis.set(versionKey, nextVersion.toString());
    return nextVersion;
  }

  /**
   * 检测冲突
   */
  private async detectConflicts(operation: Operation): Promise<Conflict[]> {
    const conflicts: Conflict[] = [];
    
    // 获取目标资源的操作历史
    const targetHistory = this.operationHistory.get(operation.targetId) || [];
    
    // 检查并发编辑冲突
    const concurrentOps = targetHistory.filter(op => 
      op.timestamp > operation.timestamp - 5000 && // 5秒内的操作
      op.userId !== operation.userId &&
      op.targetId === operation.targetId
    );
    
    if (concurrentOps.length > 0) {
      conflicts.push({
        id: uuidv4(),
        type: ConflictType.CONCURRENT_EDIT,
        operations: [operation, ...concurrentOps],
        severity: 'medium',
        autoResolvable: true,
        affectedUsers: [operation.userId, ...concurrentOps.map(op => op.userId)],
        timestamp: Date.now()
      });
    }
    
    // 检查依赖冲突
    if (operation.dependencies) {
      for (const depId of operation.dependencies) {
        const depExists = await this.checkResourceExists(depId);
        if (!depExists) {
          conflicts.push({
            id: uuidv4(),
            type: ConflictType.DEPENDENCY_VIOLATION,
            operations: [operation],
            severity: 'high',
            autoResolvable: false,
            affectedUsers: [operation.userId],
            timestamp: Date.now()
          });
        }
      }
    }
    
    return conflicts;
  }

  /**
   * 解决冲突
   */
  private async resolveConflicts(
    conflicts: Conflict[],
    operation: Operation
  ): Promise<{
    success: boolean;
    transformedOperation?: Operation;
    resolutionStrategy?: string;
  }> {
    try {
      for (const conflict of conflicts) {
        if (!conflict.autoResolvable) {
          return { success: false };
        }

        switch (conflict.type) {
          case ConflictType.CONCURRENT_EDIT:
            const transformResult = await this.transformOperation(operation, conflict.operations);
            if (transformResult.success) {
              return {
                success: true,
                transformedOperation: transformResult.transformedOperation,
                resolutionStrategy: 'operational_transformation'
              };
            }
            break;

          case ConflictType.DEPENDENCY_VIOLATION:
            // 尝试修复依赖
            const fixResult = await this.fixDependencies(operation);
            if (fixResult.success) {
              return {
                success: true,
                transformedOperation: fixResult.fixedOperation,
                resolutionStrategy: 'dependency_fix'
              };
            }
            break;
        }
      }

      return { success: false };

    } catch (error) {
      this.logger.error('解决冲突失败:', error);
      return { success: false };
    }
  }

  /**
   * 操作转换算法
   */
  private async transformOperation(
    operation: Operation,
    conflictingOps: Operation[]
  ): Promise<{
    success: boolean;
    transformedOperation?: Operation;
  }> {
    try {
      let transformedOp = { ...operation };

      // 按时间戳排序冲突操作
      const sortedOps = conflictingOps.sort((a, b) => a.timestamp - b.timestamp);

      for (const conflictOp of sortedOps) {
        transformedOp = this.applyOperationalTransform(transformedOp, conflictOp);
      }

      return {
        success: true,
        transformedOperation: transformedOp
      };

    } catch (error) {
      this.logger.error('操作转换失败:', error);
      return { success: false };
    }
  }

  /**
   * 应用操作转换
   */
  private applyOperationalTransform(op1: Operation, op2: Operation): Operation {
    // 简化的操作转换实现
    // 实际应用中需要根据具体的操作类型实现更复杂的转换逻辑

    const transformed = { ...op1 };

    // 如果两个操作作用于同一个目标
    if (op1.targetId === op2.targetId) {
      switch (op1.type) {
        case OperationType.UPDATE:
          if (op2.type === OperationType.UPDATE) {
            // 合并更新操作
            transformed.data = { ...op2.data, ...op1.data };
          } else if (op2.type === OperationType.DELETE) {
            // 如果目标已被删除，转换为创建操作
            transformed.type = OperationType.CREATE;
          }
          break;

        case OperationType.DELETE:
          if (op2.type === OperationType.DELETE) {
            // 重复删除，忽略操作
            transformed.type = OperationType.UPDATE;
            transformed.data = {};
          }
          break;

        case OperationType.MOVE:
          if (op2.type === OperationType.MOVE) {
            // 调整移动位置
            if (transformed.data.position >= op2.data.position) {
              transformed.data.position += 1;
            }
          }
          break;
      }
    }

    return transformed;
  }

  /**
   * 修复依赖关系
   */
  private async fixDependencies(operation: Operation): Promise<{
    success: boolean;
    fixedOperation?: Operation;
  }> {
    try {
      const fixed = { ...operation };

      if (operation.dependencies) {
        const validDependencies: string[] = [];

        for (const depId of operation.dependencies) {
          const exists = await this.checkResourceExists(depId);
          if (exists) {
            validDependencies.push(depId);
          } else {
            // 尝试找到替代依赖
            const alternative = await this.findAlternativeDependency(depId);
            if (alternative) {
              validDependencies.push(alternative);
            }
          }
        }

        fixed.dependencies = validDependencies;
      }

      return {
        success: true,
        fixedOperation: fixed
      };

    } catch (error) {
      this.logger.error('修复依赖失败:', error);
      return { success: false };
    }
  }

  /**
   * 检查资源是否存在
   */
  private async checkResourceExists(resourceId: string): Promise<boolean> {
    try {
      const exists = await this.redis.exists(`resource:${resourceId}`);
      return exists === 1;
    } catch (error) {
      this.logger.error('检查资源存在性失败:', error);
      return false;
    }
  }

  /**
   * 查找替代依赖
   */
  private async findAlternativeDependency(originalDepId: string): Promise<string | null> {
    try {
      // 简化实现：查找相似的资源
      const pattern = `resource:${originalDepId.split(':')[0]}:*`;
      const keys = await this.redis.keys(pattern);

      if (keys.length > 0) {
        return keys[0].replace('resource:', '');
      }

      return null;

    } catch (error) {
      this.logger.error('查找替代依赖失败:', error);
      return null;
    }
  }

  /**
   * 应用操作
   */
  private async applyOperation(operation: Operation): Promise<void> {
    try {
      // 添加到操作历史
      if (!this.operationHistory.has(operation.targetId)) {
        this.operationHistory.set(operation.targetId, []);
      }

      const history = this.operationHistory.get(operation.targetId)!;
      history.push(operation);

      // 限制历史记录大小
      if (history.length > this.maxOperationHistory) {
        history.shift();
      }

      // 保存到Redis
      await this.redis.setex(
        `collaboration:operation:${operation.id}`,
        3600 * 24, // 24小时
        JSON.stringify(operation)
      );

      // 更新会话统计
      const session = this.activeSessions.get(operation.sessionId);
      if (session) {
        session.operationCount++;
        session.lastActivity = new Date();
      }

      // 更新性能指标
      this.performanceMetrics.totalOperations++;

      // 广播操作到其他用户
      this.eventEmitter.emit('collaboration.operation.applied', operation);

      this.logger.log(`操作已应用: ${operation.id}`);

    } catch (error) {
      this.logger.error('应用操作失败:', error);
      throw error;
    }
  }

  /**
   * 添加到冲突队列
   */
  private async addToConflictQueue(conflicts: Conflict[]): Promise<void> {
    try {
      for (const conflict of conflicts) {
        const projectId = this.getProjectIdFromConflict(conflict);

        if (!this.conflictQueue.has(projectId)) {
          this.conflictQueue.set(projectId, []);
        }

        this.conflictQueue.get(projectId)!.push(conflict);

        // 保存到Redis
        await this.redis.setex(
          `collaboration:conflict:${conflict.id}`,
          3600, // 1小时
          JSON.stringify(conflict)
        );

        // 更新性能指标
        this.performanceMetrics.conflictsDetected++;

        // 通知相关用户
        this.eventEmitter.emit('collaboration.conflict.detected', conflict);
      }

    } catch (error) {
      this.logger.error('添加冲突到队列失败:', error);
    }
  }

  /**
   * 从冲突中获取项目ID
   */
  private getProjectIdFromConflict(conflict: Conflict): string {
    // 简化实现：从第一个操作中获取项目ID
    const firstOp = conflict.operations[0];
    const session = this.activeSessions.get(firstOp.sessionId);
    return session ? session.projectId : 'unknown';
  }

  /**
   * 启动冲突检测
   */
  private startConflictDetection(): void {
    setInterval(() => {
      this.performConflictDetection();
    }, this.conflictDetectionInterval);

    this.logger.log('冲突检测已启动');
  }

  /**
   * 执行冲突检测
   */
  private async performConflictDetection(): Promise<void> {
    try {
      // 检查所有活跃会话的操作
      for (const session of this.activeSessions.values()) {
        if (!session.isActive) continue;

        // 检查会话超时
        const timeSinceLastActivity = Date.now() - session.lastActivity.getTime();
        if (timeSinceLastActivity > this.sessionTimeoutMs) {
          await this.deactivateSession(session.sessionId);
          continue;
        }

        // 检查并发操作数量
        const recentOps = this.getRecentOperations(session.sessionId, 10000); // 10秒内
        if (recentOps.length > this.maxConcurrentOperations) {
          this.logger.warn(`会话 ${session.sessionId} 操作过于频繁`);
          // 可以实施限流措施
        }
      }

    } catch (error) {
      this.logger.error('冲突检测失败:', error);
    }
  }

  /**
   * 获取最近的操作
   */
  private getRecentOperations(sessionId: string, timeWindowMs: number): Operation[] {
    const cutoffTime = Date.now() - timeWindowMs;
    const recentOps: Operation[] = [];

    for (const history of this.operationHistory.values()) {
      for (const op of history) {
        if (op.sessionId === sessionId && op.timestamp > cutoffTime) {
          recentOps.push(op);
        }
      }
    }

    return recentOps;
  }

  /**
   * 停用会话
   */
  private async deactivateSession(sessionId: string): Promise<void> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (session) {
        session.isActive = false;

        // 从活跃会话中移除
        this.activeSessions.delete(sessionId);

        // 从Redis中删除
        await this.redis.del(`collaboration:session:${sessionId}`);

        // 更新性能指标
        this.performanceMetrics.activeUsers = this.activeSessions.size;

        this.eventEmitter.emit('collaboration.session.deactivated', session);
        this.logger.log(`会话已停用: ${sessionId}`);
      }

    } catch (error) {
      this.logger.error('停用会话失败:', error);
    }
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    setInterval(() => {
      this.updatePerformanceMetrics();
    }, 60000); // 每分钟更新一次

    this.logger.log('性能监控已启动');
  }

  /**
   * 更新性能指标
   */
  private async updatePerformanceMetrics(): Promise<void> {
    try {
      // 计算吞吐量（每分钟操作数）
      const oneMinuteAgo = Date.now() - 60000;
      let recentOperations = 0;

      for (const history of this.operationHistory.values()) {
        recentOperations += history.filter(op => op.timestamp > oneMinuteAgo).length;
      }

      this.performanceMetrics.throughput = recentOperations;

      // 保存性能指标到Redis
      await this.redis.setex(
        'collaboration:performance_metrics',
        300, // 5分钟
        JSON.stringify(this.performanceMetrics)
      );

    } catch (error) {
      this.logger.error('更新性能指标失败:', error);
    }
  }

  /**
   * 加载权限规则
   */
  private async loadPermissionRules(): Promise<void> {
    try {
      const ruleKeys = await this.redis.keys('collaboration:permission:*');

      for (const key of ruleKeys) {
        const ruleData = await this.redis.get(key);
        if (ruleData) {
          const rule: PermissionRule = JSON.parse(ruleData);
          const resourceKey = `${rule.resourceType}:${rule.resourceId || '*'}`;

          if (!this.permissionRules.has(resourceKey)) {
            this.permissionRules.set(resourceKey, []);
          }

          this.permissionRules.get(resourceKey)!.push(rule);
        }
      }

      this.logger.log(`已加载 ${ruleKeys.length} 个权限规则`);

    } catch (error) {
      this.logger.error('加载权限规则失败:', error);
    }
  }

  /**
   * 获取协作统计信息
   */
  public getCollaborationStats(): {
    activeSessions: number;
    totalOperations: number;
    conflictsDetected: number;
    conflictsResolved: number;
    averageResolutionTime: number;
    throughput: number;
  } {
    return {
      activeSessions: this.activeSessions.size,
      totalOperations: this.performanceMetrics.totalOperations,
      conflictsDetected: this.performanceMetrics.conflictsDetected,
      conflictsResolved: this.performanceMetrics.conflictsResolved,
      averageResolutionTime: this.performanceMetrics.averageResolutionTime,
      throughput: this.performanceMetrics.throughput
    };
  }

  /**
   * 定期清理
   */
  @Cron(CronExpression.EVERY_HOUR)
  public async performPeriodicCleanup(): Promise<void> {
    try {
      this.logger.log('开始执行定期清理');

      // 清理过期的操作历史
      const cutoffTime = Date.now() - 24 * 3600 * 1000; // 24小时前

      for (const [targetId, history] of this.operationHistory) {
        const filteredHistory = history.filter(op => op.timestamp > cutoffTime);

        if (filteredHistory.length === 0) {
          this.operationHistory.delete(targetId);
        } else {
          this.operationHistory.set(targetId, filteredHistory);
        }
      }

      // 清理已解决的冲突
      for (const [projectId, conflicts] of this.conflictQueue) {
        const activeConflicts = conflicts.filter(conflict =>
          Date.now() - conflict.timestamp < 3600000 // 1小时内的冲突
        );

        if (activeConflicts.length === 0) {
          this.conflictQueue.delete(projectId);
        } else {
          this.conflictQueue.set(projectId, activeConflicts);
        }
      }

      this.logger.log('定期清理完成');

    } catch (error) {
      this.logger.error('定期清理失败:', error);
    }
  }

  /**
   * 关闭服务
   */
  public async shutdown(): Promise<void> {
    this.logger.log('正在关闭增强协作服务...');

    // 保存当前状态
    const currentState = {
      activeSessions: Object.fromEntries(this.activeSessions),
      performanceMetrics: this.performanceMetrics,
      operationHistoryCount: this.operationHistory.size,
      conflictQueueCount: this.conflictQueue.size,
      timestamp: Date.now()
    };

    await this.redis.setex(
      'collaboration:final_state',
      3600,
      JSON.stringify(currentState)
    );

    // 停用所有会话
    for (const sessionId of this.activeSessions.keys()) {
      await this.deactivateSession(sessionId);
    }

    this.redis.disconnect();
    this.logger.log('增强协作服务已关闭');
  }
}
