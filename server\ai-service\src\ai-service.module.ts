/**
 * AI服务主模块
 */
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';

// 服务
import { AIAlgorithmService } from './services/ai-algorithm.service';
import { DistributedInferenceService } from './services/distributed-inference.service';
import { IntelligentLoadBalancerService } from './services/intelligent-load-balancer.service';
import { ModelVersionManagerService } from './services/model-version-manager.service';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
      expandVariables: true
    }),

    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // 定时任务模块
    ScheduleModule.forRoot(),
  ],

  providers: [
    AIAlgorithmService,
    DistributedInferenceService,
    IntelligentLoadBalancerService,
    ModelVersionManagerService,
    
    // 全局提供者
    {
      provide: 'APP_CONFIG',
      useFactory: (configService: ConfigService) => ({
        name: 'ai-service',
        version: '1.0.0',
        environment: configService.get<string>('NODE_ENV', 'development'),
        port: configService.get<number>('PORT', 3009),
        debug: configService.get<boolean>('DEBUG', false)
      }),
      inject: [ConfigService]
    },

    // Redis 配置
    {
      provide: 'REDIS_CONFIG',
      useFactory: (configService: ConfigService) => ({
        host: configService.get<string>('REDIS_HOST', 'localhost'),
        port: configService.get<number>('REDIS_PORT', 6379),
        password: configService.get<string>('REDIS_PASSWORD'),
        db: configService.get<number>('REDIS_DB', 0),
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
      }),
      inject: [ConfigService]
    }
  ],

  exports: [
    AIAlgorithmService,
    DistributedInferenceService,
    IntelligentLoadBalancerService,
    ModelVersionManagerService
  ]
})
export class AIServiceModule {
  constructor(private configService: ConfigService) {
    // 模块初始化日志
    console.log('AI服务模块已加载');
    console.log(`环境: ${this.configService.get<string>('NODE_ENV', 'development')}`);
    console.log(`Redis: ${this.configService.get<string>('REDIS_HOST', 'localhost')}:${this.configService.get<number>('REDIS_PORT', 6379)}`);
  }
}
