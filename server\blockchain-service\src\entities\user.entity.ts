/**
 * 用户实体
 */

import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { BlockchainAsset } from './blockchain-asset.entity';
import { Transaction } from './transaction.entity';
import { MarketplaceListing } from './marketplace-listing.entity';

@Entity('users')
@Index(['email'], { unique: true })
@Index(['walletAddress'], { unique: true })
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  username: string;

  @Column({ type: 'varchar', length: 255, unique: true, nullable: false })
  email: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  passwordHash: string;

  @Column({ type: 'varchar', length: 42, unique: true, nullable: true })
  walletAddress: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  firstName: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  lastName: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  avatar: string;

  @Column({ type: 'text', nullable: true })
  bio: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  website: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  twitter: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  discord: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isVerified: boolean;

  @Column({ type: 'enum', enum: ['user', 'creator', 'admin'], default: 'user' })
  role: string;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  balance: number;

  @Column({ type: 'json', nullable: true })
  preferences: Record<string, any>;

  @Column({ type: 'timestamp', nullable: true })
  lastLoginAt: Date;

  @Column({ type: 'varchar', length: 45, nullable: true })
  lastLoginIp: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => BlockchainAsset, asset => asset.owner)
  ownedAssets: BlockchainAsset[];

  @OneToMany(() => BlockchainAsset, asset => asset.creator)
  createdAssets: BlockchainAsset[];

  @OneToMany(() => Transaction, transaction => transaction.fromUser)
  sentTransactions: Transaction[];

  @OneToMany(() => Transaction, transaction => transaction.toUser)
  receivedTransactions: Transaction[];

  @OneToMany(() => MarketplaceListing, listing => listing.seller)
  listings: MarketplaceListing[];
}
