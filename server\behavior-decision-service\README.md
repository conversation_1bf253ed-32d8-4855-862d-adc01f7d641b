# 分布式行为决策服务

## 概述

分布式行为决策服务提供大规模分布式环境下的行为决策和协调功能，支持多实例协调、负载均衡、故障恢复等企业级特性。

## 主要功能

- **分布式决策**: 支持多节点协同决策
- **负载均衡**: 智能分配决策请求到最优节点
- **故障恢复**: 自动检测和处理节点故障
- **实时监控**: 提供节点状态和性能监控
- **策略配置**: 支持多种协调策略

## 技术架构

- **框架**: NestJS
- **消息队列**: Redis Pub/Sub
- **缓存**: Redis
- **调度**: @nestjs/schedule
- **事件**: @nestjs/event-emitter

## 安装和运行

### 安装依赖
```bash
npm install
```

### 配置环境变量
复制 `.env` 文件并根据需要修改配置。

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

## API 接口

### 提交决策请求
```
POST /behavior/decision
```

### 获取节点状态
```
GET /behavior/status
```

### 获取所有节点状态
```
GET /behavior/nodes
```

### 设置协调策略
```
POST /behavior/strategy/:strategy
```

### 健康检查
```
GET /behavior/health
```

## 协调策略

- `round_robin`: 轮询分配
- `least_load`: 最小负载优先
- `geographic`: 地理位置优先
- `capability_based`: 能力匹配优先

## 监控指标

- 节点负载
- 处理请求数
- 平均响应时间
- 错误率
- 心跳状态

## 故障处理

服务具备以下故障处理能力：

1. **节点故障检测**: 通过心跳机制检测节点状态
2. **自动故障转移**: 将请求重新路由到健康节点
3. **负载重平衡**: 动态调整负载分配
4. **数据一致性**: 确保决策结果的一致性

## 配置说明

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| PORT | 服务端口 | 3008 |
| REDIS_HOST | Redis主机 | localhost |
| REDIS_PORT | Redis端口 | 6379 |
| NODE_ID | 节点标识 | behavior-node-1 |
| MAX_CAPACITY | 最大处理能力 | 100 |
