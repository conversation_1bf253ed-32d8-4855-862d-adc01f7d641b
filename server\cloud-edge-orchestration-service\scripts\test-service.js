/**
 * 服务测试脚本
 */

const http = require('http');

console.log('🧪 开始测试云边协同编排服务...\n');

// 测试服务是否启动
function testServiceHealth() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3003,
      path: '/health',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: response
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.end();
  });
}

// 测试API文档
function testApiDocs() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3003,
      path: '/api/docs',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      resolve({
        status: res.statusCode,
        contentType: res.headers['content-type']
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.end();
  });
}

// 执行测试
async function runTests() {
  console.log('🔍 检查服务健康状态...');
  
  try {
    const healthResult = await testServiceHealth();
    console.log(`✅ 健康检查成功 (状态码: ${healthResult.status})`);
    console.log(`📊 响应数据:`, JSON.stringify(healthResult.data, null, 2));
  } catch (error) {
    console.log(`❌ 健康检查失败: ${error.message}`);
    console.log('💡 可能的原因:');
    console.log('   - 服务未启动');
    console.log('   - 端口3003被占用');
    console.log('   - 防火墙阻止连接');
    return;
  }

  console.log('\n📚 检查API文档...');
  
  try {
    const docsResult = await testApiDocs();
    console.log(`✅ API文档访问成功 (状态码: ${docsResult.status})`);
    console.log(`📄 内容类型: ${docsResult.contentType}`);
  } catch (error) {
    console.log(`❌ API文档访问失败: ${error.message}`);
  }

  console.log('\n🎉 服务测试完成！');
  console.log('🌐 服务地址: http://localhost:3003');
  console.log('📚 API文档: http://localhost:3003/api/docs');
}

// 检查端口是否被占用
function checkPort() {
  return new Promise((resolve) => {
    const server = http.createServer();
    
    server.listen(3003, () => {
      server.close(() => {
        resolve(false); // 端口可用
      });
    });
    
    server.on('error', () => {
      resolve(true); // 端口被占用
    });
  });
}

async function main() {
  console.log('🔌 检查端口3003状态...');
  
  const portInUse = await checkPort();
  
  if (portInUse) {
    console.log('✅ 端口3003正在使用中 (服务可能已启动)');
    await runTests();
  } else {
    console.log('❌ 端口3003未被使用');
    console.log('💡 请先启动服务:');
    console.log('   npm run start:dev');
    console.log('   或');
    console.log('   npm run start:prod');
  }
}

main().catch(console.error);
