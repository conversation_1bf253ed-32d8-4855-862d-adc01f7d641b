/**
 * 智能合约实体
 */

import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { BlockchainAsset } from './blockchain-asset.entity';
import { Transaction } from './transaction.entity';

@Entity('smart_contracts')
@Index(['address'], { unique: true })
@Index(['blockchain'])
export class SmartContract {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 42, unique: true, nullable: false })
  address: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @Column({ type: 'varchar', length: 20, nullable: false })
  symbol: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: ['ERC721', 'ERC1155', 'ERC20', 'Custom'], nullable: false })
  type: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  blockchain: string;

  @Column({ type: 'int', nullable: false })
  chainId: number;

  @Column({ type: 'json', nullable: true })
  abi: Record<string, any>[];

  @Column({ type: 'text', nullable: true })
  bytecode: string;

  @Column({ type: 'varchar', length: 42, nullable: true })
  deployer: string;

  @Column({ type: 'varchar', length: 66, nullable: true })
  deploymentTxHash: string;

  @Column({ type: 'bigint', nullable: true })
  deploymentBlockNumber: number;

  @Column({ type: 'timestamp', nullable: true })
  deployedAt: Date;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isVerified: boolean;

  @Column({ type: 'boolean', default: false })
  isPaused: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  royaltyPercentage: number;

  @Column({ type: 'varchar', length: 42, nullable: true })
  royaltyRecipient: string;

  @Column({ type: 'bigint', nullable: true })
  maxSupply: number;

  @Column({ type: 'bigint', default: 0 })
  totalSupply: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, nullable: true })
  mintPrice: number;

  @Column({ type: 'varchar', length: 42, nullable: true })
  paymentToken: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  baseTokenURI: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  contractURI: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  settings: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => BlockchainAsset, asset => asset.contract)
  assets: BlockchainAsset[];

  @OneToMany(() => Transaction, transaction => transaction.contract)
  transactions: Transaction[];
}
