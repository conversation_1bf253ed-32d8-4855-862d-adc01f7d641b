/**
 * 云边协同控制器
 */

import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { CloudEdgeOrchestratorService } from '../../orchestration/cloud-edge-orchestrator.service';
import { EnhancedEdgeManagerService } from '../../orchestration/enhanced-edge-manager.service';

@ApiTags('cloud-edge')
@Controller('cloud-edge')
export class CloudEdgeController {
  constructor(
    private readonly orchestratorService: CloudEdgeOrchestratorService,
    private readonly edgeManagerService: EnhancedEdgeManagerService,
  ) {}

  @Get('status')
  @ApiOperation({ summary: '获取云边协同状态' })
  @ApiResponse({ status: 200, description: '返回云边协同状态信息' })
  async getCloudEdgeStatus() {
    return this.orchestratorService.getCloudEdgeStatus();
  }

  @Post('resources/cloud')
  @ApiOperation({ summary: '注册云资源' })
  @ApiBody({
    description: '云资源配置',
    schema: {
      type: 'object',
      properties: {
        type: { type: 'string', example: 'compute' },
        region: { type: 'string', example: 'us-west-1' },
        specifications: { type: 'object' },
        tags: { type: 'object' }
      }
    }
  })
  @ApiResponse({ status: 201, description: '云资源注册成功' })
  async registerCloudResource(@Body() resourceConfig: any) {
    return this.orchestratorService.registerCloudResource(resourceConfig);
  }

  @Post('nodes/edge')
  @ApiOperation({ summary: '注册边缘节点' })
  @ApiBody({
    description: '边缘节点配置',
    schema: {
      type: 'object',
      properties: {
        nodeId: { type: 'string', example: 'edge-node-001' },
        name: { type: 'string', example: '边缘节点1' },
        location: { type: 'object' },
        capabilities: { type: 'object' }
      }
    }
  })
  @ApiResponse({ status: 201, description: '边缘节点注册成功' })
  async registerEdgeNode(@Body() nodeConfig: any) {
    return this.orchestratorService.registerEdgeNode(nodeConfig);
  }

  @Post('workloads/deploy')
  @ApiOperation({ summary: '部署工作负载' })
  @ApiBody({
    description: '工作负载配置',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'my-workload' },
        image: { type: 'string', example: 'nginx:latest' },
        resources: { type: 'object' },
        placement: { type: 'object' }
      }
    }
  })
  @ApiResponse({ status: 201, description: '工作负载部署成功' })
  async deployWorkload(@Body() workloadConfig: any) {
    return this.orchestratorService.deployWorkload(workloadConfig);
  }

  @Post('load-balancing/optimize')
  @ApiOperation({ summary: '执行智能负载均衡' })
  @ApiBody({
    description: '负载均衡策略',
    schema: {
      type: 'object',
      properties: {
        strategy: { type: 'string', example: 'round-robin' },
        targetUtilization: { type: 'number', example: 70 }
      }
    }
  })
  @ApiResponse({ status: 200, description: '负载均衡执行成功' })
  async optimizeLoadBalancing(@Body() balancingStrategy: any) {
    return this.orchestratorService.intelligentLoadBalancing(balancingStrategy.strategy);
  }

  @Post('network-slicing/create')
  @ApiOperation({ summary: '创建网络切片' })
  @ApiBody({
    description: '网络切片配置',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'slice-001' },
        type: { type: 'string', example: 'eMBB' },
        bandwidth: { type: 'string', example: '100Mbps' },
        latency: { type: 'string', example: '1ms' }
      }
    }
  })
  @ApiResponse({ status: 201, description: '网络切片创建成功' })
  async createNetworkSlice(@Body() sliceConfig: any) {
    return this.orchestratorService.create5GNetworkSlice(sliceConfig);
  }

  @Post('optimization/resources')
  @ApiOperation({ summary: '执行资源优化' })
  @ApiBody({
    description: '优化目标',
    schema: {
      type: 'object',
      properties: {
        goals: { 
          type: 'array', 
          items: { type: 'string' },
          example: ['cost-reduction', 'performance-improvement'] 
        }
      }
    }
  })
  @ApiResponse({ status: 200, description: '资源优化执行成功' })
  async optimizeResources(@Body() optimizationGoals: any) {
    return this.orchestratorService.performResourceOptimization(optimizationGoals.goals);
  }

  @Get('metrics/performance')
  @ApiOperation({ summary: '获取性能指标' })
  @ApiResponse({ status: 200, description: '返回性能指标数据' })
  async getPerformanceMetrics() {
    return this.orchestratorService.getPerformanceMetrics();
  }

  @Delete('nodes/edge/:nodeId')
  @ApiOperation({ summary: '移除边缘节点' })
  @ApiParam({ name: 'nodeId', description: '边缘节点ID' })
  @ApiResponse({ status: 200, description: '边缘节点移除成功' })
  async removeEdgeNode(@Param('nodeId') nodeId: string) {
    return this.edgeManagerService.removeEdgeNode(nodeId);
  }
}
