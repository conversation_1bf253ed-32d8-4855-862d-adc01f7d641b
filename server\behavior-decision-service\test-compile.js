/**
 * 简单的编译测试脚本
 */

const fs = require('fs');
const path = require('path');

console.log('开始检查分布式行为决策服务文件...');

// 检查必要文件是否存在
const requiredFiles = [
  'package.json',
  'tsconfig.json',
  'nest-cli.json',
  'src/main.ts',
  'src/app.module.ts',
  'src/services/distributed-behavior.service.ts',
  'src/controllers/behavior.controller.ts'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✓ ${file} 存在`);
  } else {
    console.log(`✗ ${file} 不存在`);
    allFilesExist = false;
  }
});

if (allFilesExist) {
  console.log('\n✓ 所有必要文件都存在');
  console.log('✓ 分布式行为决策服务结构完整');
} else {
  console.log('\n✗ 缺少必要文件');
  process.exit(1);
}

// 检查主要导入
try {
  const serviceContent = fs.readFileSync(path.join(__dirname, 'src/services/distributed-behavior.service.ts'), 'utf8');
  
  if (serviceContent.includes('import { Injectable, Logger, Inject }')) {
    console.log('✓ 服务导入正确');
  } else {
    console.log('✗ 服务导入有问题');
  }
  
  if (serviceContent.includes('@Injectable()')) {
    console.log('✓ 服务装饰器正确');
  } else {
    console.log('✗ 服务装饰器缺失');
  }
  
} catch (error) {
  console.log('✗ 读取服务文件失败:', error.message);
}

// 检查导入路径
try {
  const serviceContent = fs.readFileSync(path.join(__dirname, 'src/services/distributed-behavior.service.ts'), 'utf8');

  if (serviceContent.includes('@engine/ai/behavior/IntelligentDecisionSystem')) {
    console.log('✓ 引擎模块导入路径正确');
  } else if (serviceContent.includes('engine/src/ai/behavior/IntelligentDecisionSystem')) {
    console.log('⚠ 引擎模块使用相对路径导入');
  } else {
    console.log('✗ 引擎模块导入路径有问题');
  }

} catch (error) {
  console.log('✗ 检查导入路径失败:', error.message);
}

// 检查 app.module 导入
try {
  const mainContent = fs.readFileSync(path.join(__dirname, 'src/main.ts'), 'utf8');

  if (mainContent.includes("from './app.module'")) {
    console.log('✓ app.module 导入路径正确');
  } else {
    console.log('✗ app.module 导入路径有问题');
  }

} catch (error) {
  console.log('✗ 检查 main.ts 失败:', error.message);
}

console.log('\n检查完成！');
