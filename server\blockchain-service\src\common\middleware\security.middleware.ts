/**
 * 安全中间件 - 提供全面的安全防护
 */

import { Injectable, NestMiddleware, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';

// 扩展Request接口以支持session
declare module 'express-serve-static-core' {
  interface Request {
    session?: {
      csrfToken?: string;
      [key: string]: any;
    };
  }
}

export interface SecurityConfig {
  enableRateLimit: boolean;
  enableCSRFProtection: boolean;
  enableXSSProtection: boolean;
  enableSQLInjectionProtection: boolean;
  enableIPWhitelist: boolean;
  enableRequestSizeLimit: boolean;
  maxRequestSize: string;
  rateLimitWindow: number;
  rateLimitMax: number;
  trustedProxies: string[];
  allowedOrigins: string[];
  ipWhitelist: string[];
}

@Injectable()
export class SecurityMiddleware implements NestMiddleware {
  private readonly logger = new Logger(SecurityMiddleware.name);
  private readonly securityConfig: SecurityConfig;
  private readonly suspiciousIPs: Map<string, number> = new Map();
  private readonly blockedIPs: Set<string> = new Set();

  constructor(private readonly configService: ConfigService) {
    this.securityConfig = {
      enableRateLimit: this.configService.get('SECURITY_ENABLE_RATE_LIMIT', true),
      enableCSRFProtection: this.configService.get('SECURITY_ENABLE_CSRF', true),
      enableXSSProtection: this.configService.get('SECURITY_ENABLE_XSS', true),
      enableSQLInjectionProtection: this.configService.get('SECURITY_ENABLE_SQL_INJECTION', true),
      enableIPWhitelist: this.configService.get('SECURITY_ENABLE_IP_WHITELIST', false),
      enableRequestSizeLimit: this.configService.get('SECURITY_ENABLE_REQUEST_SIZE_LIMIT', true),
      maxRequestSize: this.configService.get('SECURITY_MAX_REQUEST_SIZE', '10mb'),
      rateLimitWindow: this.configService.get('SECURITY_RATE_LIMIT_WINDOW', 15 * 60 * 1000), // 15分钟
      rateLimitMax: this.configService.get('SECURITY_RATE_LIMIT_MAX', 100),
      trustedProxies: this.configService.get('SECURITY_TRUSTED_PROXIES', '').split(',').filter(Boolean),
      allowedOrigins: this.configService.get('SECURITY_ALLOWED_ORIGINS', '').split(',').filter(Boolean),
      ipWhitelist: this.configService.get('SECURITY_IP_WHITELIST', '').split(',').filter(Boolean),
    };

    // 定期清理可疑IP记录
    setInterval(() => {
      this.cleanupSuspiciousIPs();
    }, 60 * 60 * 1000); // 每小时清理一次
  }

  use(req: Request, res: Response, next: NextFunction): void {
    try {
      // 获取客户端IP
      const clientIP = this.getClientIP(req);
      
      // 检查IP是否被阻止
      if (this.blockedIPs.has(clientIP)) {
        this.logger.warn(`阻止被封禁IP的请求: ${clientIP}`);
        throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
      }

      // IP白名单检查
      if (this.securityConfig.enableIPWhitelist && !this.isIPWhitelisted(clientIP)) {
        this.logger.warn(`IP不在白名单中: ${clientIP}`);
        throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
      }

      // 安全头设置
      this.setSecurityHeaders(res);

      // CSRF保护
      if (this.securityConfig.enableCSRFProtection) {
        this.checkCSRFToken(req);
      }

      // XSS保护
      if (this.securityConfig.enableXSSProtection) {
        this.checkXSSAttack(req);
      }

      // SQL注入保护
      if (this.securityConfig.enableSQLInjectionProtection) {
        this.checkSQLInjection(req);
      }

      // 检查可疑活动
      this.checkSuspiciousActivity(req, clientIP);

      // 记录安全日志
      this.logSecurityEvent(req, clientIP, 'request_allowed');

      next();
    } catch (error) {
      this.handleSecurityError(error, req, res);
    }
  }

  /**
   * 获取客户端IP地址
   */
  private getClientIP(req: Request): string {
    const forwarded = req.headers['x-forwarded-for'] as string;
    const realIP = req.headers['x-real-ip'] as string;
    
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    if (realIP) {
      return realIP;
    }
    
    return req.socket.remoteAddress || 'unknown';
  }

  /**
   * 检查IP是否在白名单中
   */
  private isIPWhitelisted(ip: string): boolean {
    if (this.securityConfig.ipWhitelist.length === 0) {
      return true;
    }
    
    return this.securityConfig.ipWhitelist.some(whitelistedIP => {
      if (whitelistedIP.includes('/')) {
        // CIDR格式
        return this.isIPInCIDR(ip, whitelistedIP);
      } else {
        // 精确匹配
        return ip === whitelistedIP;
      }
    });
  }

  /**
   * 检查IP是否在CIDR范围内
   */
  private isIPInCIDR(ip: string, cidr: string): boolean {
    // 简化实现，实际应用中应使用专门的IP库
    const [network, prefixLength] = cidr.split('/');
    // 这里需要实现CIDR匹配逻辑
    return ip.startsWith(network.split('.').slice(0, Math.floor(parseInt(prefixLength) / 8)).join('.'));
  }

  /**
   * 设置安全响应头
   */
  private setSecurityHeaders(res: Response): void {
    // 防止点击劫持
    res.setHeader('X-Frame-Options', 'DENY');
    
    // 防止MIME类型嗅探
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    // XSS保护
    res.setHeader('X-XSS-Protection', '1; mode=block');
    
    // 强制HTTPS
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    
    // 内容安全策略
    res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
    
    // 引用者策略
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // 权限策略
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  }

  /**
   * 检查CSRF令牌
   */
  private checkCSRFToken(req: Request): void {
    const method = req.method.toLowerCase();
    
    // 只对状态改变的请求检查CSRF
    if (['post', 'put', 'patch', 'delete'].includes(method)) {
      const token = req.headers['x-csrf-token'] as string || req.body._csrf;
      const sessionToken = req.session?.csrfToken;
      
      if (!token || !sessionToken || token !== sessionToken) {
        this.logger.warn(`CSRF令牌验证失败: ${req.url}`);
        throw new HttpException('Invalid CSRF token', HttpStatus.FORBIDDEN);
      }
    }
  }

  /**
   * 检查XSS攻击
   */
  private checkXSSAttack(req: Request): void {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
      /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
    ];

    const checkString = (str: string): boolean => {
      return xssPatterns.some(pattern => pattern.test(str));
    };

    const checkObject = (obj: any): boolean => {
      if (typeof obj === 'string') {
        return checkString(obj);
      }
      
      if (typeof obj === 'object' && obj !== null) {
        return Object.values(obj).some(value => checkObject(value));
      }
      
      return false;
    };

    // 检查查询参数
    if (checkObject(req.query)) {
      this.logger.warn(`检测到XSS攻击尝试 (query): ${req.url}`);
      throw new HttpException('Potential XSS attack detected', HttpStatus.BAD_REQUEST);
    }

    // 检查请求体
    if (req.body && checkObject(req.body)) {
      this.logger.warn(`检测到XSS攻击尝试 (body): ${req.url}`);
      throw new HttpException('Potential XSS attack detected', HttpStatus.BAD_REQUEST);
    }

    // 检查请求头
    const suspiciousHeaders = ['user-agent', 'referer', 'x-forwarded-for'];
    for (const header of suspiciousHeaders) {
      const value = req.headers[header] as string;
      if (value && checkString(value)) {
        this.logger.warn(`检测到XSS攻击尝试 (header ${header}): ${value}`);
        throw new HttpException('Potential XSS attack detected', HttpStatus.BAD_REQUEST);
      }
    }
  }

  /**
   * 检查SQL注入攻击
   */
  private checkSQLInjection(req: Request): void {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
      /('|(\\')|(;)|(--)|(\s)|(\/\*)|(\*\/))/gi,
      /(\b(WAITFOR|DELAY)\b)/gi,
      /(\b(BENCHMARK|SLEEP)\b)/gi,
    ];

    const checkString = (str: string): boolean => {
      return sqlPatterns.some(pattern => pattern.test(str));
    };

    const checkObject = (obj: any): boolean => {
      if (typeof obj === 'string') {
        return checkString(obj);
      }
      
      if (typeof obj === 'object' && obj !== null) {
        return Object.values(obj).some(value => checkObject(value));
      }
      
      return false;
    };

    // 检查查询参数
    if (checkObject(req.query)) {
      this.logger.warn(`检测到SQL注入攻击尝试 (query): ${req.url}`);
      throw new HttpException('Potential SQL injection detected', HttpStatus.BAD_REQUEST);
    }

    // 检查请求体
    if (req.body && checkObject(req.body)) {
      this.logger.warn(`检测到SQL注入攻击尝试 (body): ${req.url}`);
      throw new HttpException('Potential SQL injection detected', HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 检查可疑活动
   */
  private checkSuspiciousActivity(req: Request, clientIP: string): void {
    const suspiciousIndicators = [
      // 异常的User-Agent
      req.headers['user-agent']?.includes('bot') && !req.headers['user-agent']?.includes('Googlebot'),
      
      // 异常的请求频率
      this.isHighFrequencyRequest(clientIP),
      
      // 异常的请求路径
      req.url.includes('..') || req.url.includes('//'),
      
      // 异常的请求方法
      !['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'].includes(req.method),
      
      // 异常的请求头
      (typeof req.headers['x-forwarded-for'] === 'string' && req.headers['x-forwarded-for'].split(',').length > 5),
    ];

    const suspiciousCount = suspiciousIndicators.filter(Boolean).length;
    
    if (suspiciousCount > 0) {
      const currentScore = this.suspiciousIPs.get(clientIP) || 0;
      const newScore = currentScore + suspiciousCount;
      this.suspiciousIPs.set(clientIP, newScore);
      
      this.logger.warn(`可疑活动检测 IP: ${clientIP}, 分数: ${newScore}`);
      
      // 如果可疑分数过高，临时阻止该IP
      if (newScore > 10) {
        this.blockedIPs.add(clientIP);
        this.logger.error(`IP已被临时阻止: ${clientIP}`);
        
        // 设置自动解封时间（1小时）
        setTimeout(() => {
          this.blockedIPs.delete(clientIP);
          this.suspiciousIPs.delete(clientIP);
          this.logger.log(`IP已自动解封: ${clientIP}`);
        }, 60 * 60 * 1000);
      }
    }
  }

  /**
   * 检查是否为高频请求
   */
  private isHighFrequencyRequest(clientIP: string): boolean {
    const key = `freq_${clientIP}`;
    const now = Date.now();
    const window = 60 * 1000; // 1分钟窗口
    const maxRequests = 100; // 每分钟最多100个请求
    
    // 这里应该使用Redis等外部存储，这里简化为内存存储
    const requests = (global as any)[key] || [];
    
    // 清理过期的请求记录
    const validRequests = requests.filter((time: number) => now - time < window);
    
    // 添加当前请求
    validRequests.push(now);
    
    // 更新记录
    (global as any)[key] = validRequests;
    
    return validRequests.length > maxRequests;
  }

  /**
   * 记录安全事件
   */
  private logSecurityEvent(req: Request, clientIP: string, eventType: string): void {
    const securityLog = {
      timestamp: new Date().toISOString(),
      ip: clientIP,
      method: req.method,
      url: req.url,
      userAgent: req.headers['user-agent'],
      eventType,
      headers: this.sanitizeHeaders(req.headers),
    };

    // 这里应该发送到专门的安全日志系统
    this.logger.debug(`安全事件: ${JSON.stringify(securityLog)}`);
  }

  /**
   * 清理敏感请求头
   */
  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    
    // 移除敏感信息
    delete sanitized.authorization;
    delete sanitized.cookie;
    delete sanitized['x-api-key'];
    
    return sanitized;
  }

  /**
   * 清理可疑IP记录
   */
  private cleanupSuspiciousIPs(): void {
    for (const [ip, score] of this.suspiciousIPs.entries()) {
      // 如果分数较低，清理记录
      if (score < 5) {
        this.suspiciousIPs.delete(ip);
      }
    }

    this.logger.debug(`清理了可疑IP记录，当前记录数: ${this.suspiciousIPs.size}`);
  }

  /**
   * 处理安全错误
   */
  private handleSecurityError(error: any, req: Request, res: Response): void {
    const clientIP = this.getClientIP(req);
    
    // 记录安全事件
    this.logSecurityEvent(req, clientIP, 'security_violation');
    
    // 增加可疑分数
    const currentScore = this.suspiciousIPs.get(clientIP) || 0;
    this.suspiciousIPs.set(clientIP, currentScore + 5);
    
    if (error instanceof HttpException) {
      res.status(error.getStatus()).json({
        statusCode: error.getStatus(),
        message: error.message,
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Internal server error',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 手动阻止IP
   */
  blockIP(ip: string): void {
    this.blockedIPs.add(ip);
    this.logger.warn(`IP已被手动阻止: ${ip}`);
  }

  /**
   * 解除IP阻止
   */
  unblockIP(ip: string): void {
    this.blockedIPs.delete(ip);
    this.suspiciousIPs.delete(ip);
    this.logger.log(`IP已被解除阻止: ${ip}`);
  }

  /**
   * 获取安全统计
   */
  getSecurityStats(): {
    blockedIPs: number;
    suspiciousIPs: number;
    topSuspiciousIPs: Array<{ ip: string; score: number }>;
  } {
    const topSuspicious = Array.from(this.suspiciousIPs.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([ip, score]) => ({ ip, score }));

    return {
      blockedIPs: this.blockedIPs.size,
      suspiciousIPs: this.suspiciousIPs.size,
      topSuspiciousIPs: topSuspicious,
    };
  }
}
