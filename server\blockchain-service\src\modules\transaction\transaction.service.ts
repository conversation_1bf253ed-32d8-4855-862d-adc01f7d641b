/**
 * 交易服务
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transaction } from '../../entities/transaction.entity';

@Injectable()
export class TransactionService {
  private readonly logger = new Logger(TransactionService.name);

  constructor(
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
  ) {}

  /**
   * 创建交易记录
   */
  async createTransaction(transactionData: Partial<Transaction>) {
    const transaction = this.transactionRepository.create(transactionData);
    return this.transactionRepository.save(transaction);
  }

  /**
   * 获取交易详情
   */
  async getTransactionById(id: string) {
    return this.transactionRepository.findOne({
      where: { id },
      relations: ['fromUser', 'toUser', 'asset', 'contract'],
    });
  }

  /**
   * 根据哈希获取交易
   */
  async getTransactionByHash(hash: string) {
    return this.transactionRepository.findOne({
      where: { hash },
      relations: ['fromUser', 'toUser', 'asset', 'contract'],
    });
  }

  /**
   * 获取用户交易历史
   */
  async getUserTransactions(userId: string) {
    return this.transactionRepository.find({
      where: [
        { fromUserId: userId },
        { toUserId: userId }
      ],
      relations: ['fromUser', 'toUser', 'asset'],
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 更新交易状态
   */
  async updateTransactionStatus(hash: string, status: string, confirmations?: number) {
    const updateData: any = { status, updatedAt: new Date() };
    if (confirmations !== undefined) {
      updateData.confirmations = confirmations;
    }
    await this.transactionRepository.update({ hash }, updateData);
    return this.getTransactionByHash(hash);
  }
}
