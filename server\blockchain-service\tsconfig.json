{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./src", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "moduleResolution": "node", "esModuleInterop": true, "resolveJsonModule": true, "paths": {"@/*": ["./*"], "@/config/*": ["./config/*"], "@/entities/*": ["./entities/*"], "@/modules/*": ["./modules/*"], "@/common/*": ["./common/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}