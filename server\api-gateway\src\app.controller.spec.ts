import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigService } from '@nestjs/config';
import { ClientProxy } from '@nestjs/microservices';
import { of } from 'rxjs';

describe('AppController', () => {
  let appController: AppController;

  beforeEach(async () => {
    const mockClientProxy: Partial<ClientProxy> = {
      connect: jest.fn().mockResolvedValue(true),
      send: jest.fn().mockReturnValue(of({ status: 'up' })),
      close: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {
        switch (key) {
          case 'NODE_ENV':
            return 'test';
          default:
            return defaultValue;
        }
      }),
    };

    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [
        AppService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: 'SERVICE_REGISTRY',
          useValue: mockClientProxy,
        },
        {
          provide: 'USER_SERVICE',
          useValue: mockClientProxy,
        },
        {
          provide: 'PROJECT_SERVICE',
          useValue: mockClientProxy,
        },
        {
          provide: 'ASSET_SERVICE',
          useValue: mockClientProxy,
        },
        {
          provide: 'RENDER_SERVICE',
          useValue: mockClientProxy,
        },
      ],
    }).compile();

    appController = app.get<AppController>(AppController);
  });

  describe('getInfo', () => {
    it('should return API gateway info', () => {
      const result = appController.getInfo();
      expect(result).toEqual({
        name: 'DL（Digital Learning）引擎API网关',
        version: '1.0.0',
        description: 'DL（Digital Learning）引擎API网关，作为前端和微服务之间的中介',
        environment: 'test',
      });
      expect(result).toHaveProperty('name');
      expect(result).toHaveProperty('version');
      expect(result).toHaveProperty('description');
      expect(result).toHaveProperty('environment');
    });
  });

  describe('healthCheck', () => {
    it('should return health status', async () => {
      const result = await appController.healthCheck();
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('services');
      expect(result.services).toHaveProperty('gateway');
      expect(result.services).toHaveProperty('serviceRegistry');
      expect(result.services).toHaveProperty('userService');
      expect(result.services).toHaveProperty('projectService');
      expect(result.services).toHaveProperty('assetService');
      expect(result.services).toHaveProperty('renderService');
      expect(['up', 'degraded']).toContain(result.status);
    });
  });
});
