/**
 * 区块链服务
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { SmartContract } from '../../entities/smart-contract.entity';
import { Transaction } from '../../entities/transaction.entity';

@Injectable()
export class BlockchainService {
  private readonly logger = new Logger(BlockchainService.name);

  constructor(
    @InjectRepository(SmartContract)
    private smartContractRepository: Repository<SmartContract>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    private configService: ConfigService,
  ) {}

  /**
   * 获取区块链网络信息
   */
  async getNetworkInfo(blockchain: string) {
    const config = this.configService.get(`blockchain.${blockchain}`);
    return {
      blockchain,
      rpcUrl: config?.rpcUrl,
      chainId: config?.chainId,
      gasPrice: config?.gasPrice,
    };
  }

  /**
   * 获取交易详情
   */
  async getTransaction(hash: string) {
    return this.transactionRepository.findOne({
      where: { hash },
      relations: ['fromUser', 'toUser', 'asset', 'contract'],
    });
  }

  /**
   * 创建交易记录
   */
  async createTransaction(transactionData: Partial<Transaction>) {
    const transaction = this.transactionRepository.create(transactionData);
    return this.transactionRepository.save(transaction);
  }

  /**
   * 更新交易状态
   */
  async updateTransactionStatus(hash: string, status: string, confirmations?: number) {
    await this.transactionRepository.update(
      { hash },
      { status, confirmations, updatedAt: new Date() }
    );
  }
}
