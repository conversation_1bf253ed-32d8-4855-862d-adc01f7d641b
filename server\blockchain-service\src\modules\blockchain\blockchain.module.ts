/**
 * 区块链模块
 */

import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { BlockchainService } from './blockchain.service';
import { BlockchainController } from './blockchain.controller';
import { SmartContract } from '../../entities/smart-contract.entity';
import { Transaction } from '../../entities/transaction.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([SmartContract, Transaction]),
    BullModule.registerQueue({
      name: 'blockchain',
    }),
  ],
  controllers: [BlockchainController],
  providers: [BlockchainService],
  exports: [BlockchainService],
})
export class BlockchainModule {}
