{"name": "dl-engine-api-gateway", "version": "0.1.0", "description": "DL（Digital Learning）引擎API网关", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.4.0", "@nestjs/config": "^3.2.0", "@nestjs/core": "^10.4.0", "@nestjs/event-emitter": "^2.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.0", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^10.4.0", "@nestjs/swagger": "^7.4.0", "@nestjs/throttler": "^6.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "express": "^5.1.0", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.6.1", "multer": "^1.4.5-lts.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^10.4.0", "@nestjs/schematics": "^10.1.0", "@nestjs/testing": "^10.4.0", "@types/compression": "^1.8.1", "@types/express": "^4.17.23", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}