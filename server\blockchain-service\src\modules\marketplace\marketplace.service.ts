/**
 * 市场服务
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MarketplaceListing } from '../../entities/marketplace-listing.entity';

@Injectable()
export class MarketplaceService {
  private readonly logger = new Logger(MarketplaceService.name);

  constructor(
    @InjectRepository(MarketplaceListing)
    private listingRepository: Repository<MarketplaceListing>,
  ) {}

  /**
   * 创建市场列表
   */
  async createListing(listingData: Partial<MarketplaceListing>) {
    const listing = this.listingRepository.create(listingData);
    return this.listingRepository.save(listing);
  }

  /**
   * 获取列表详情
   */
  async getListingById(id: string) {
    return this.listingRepository.findOne({
      where: { id },
      relations: ['seller', 'buyer', 'asset'],
    });
  }

  /**
   * 获取活跃的市场列表
   */
  async getActiveListings() {
    return this.listingRepository.find({
      where: { status: 'active' },
      relations: ['seller', 'asset'],
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 获取用户的列表
   */
  async getListingsBySeller(sellerId: string) {
    return this.listingRepository.find({
      where: { sellerId },
      relations: ['asset'],
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 更新列表状态
   */
  async updateListingStatus(id: string, status: string, buyerId?: string) {
    const updateData: any = { status, updatedAt: new Date() };
    if (buyerId) {
      updateData.buyerId = buyerId;
      updateData.soldAt = new Date();
    }
    await this.listingRepository.update(id, updateData);
    return this.getListingById(id);
  }
}
