/**
 * 测试模块导入
 */

// 测试 app.module 导入
try {
  console.log('测试 AppModule 导入...');
  const { AppModule } = require('./src/app.module');
  console.log('✅ AppModule 导入成功:', AppModule.name);
} catch (error) {
  console.error('❌ AppModule 导入失败:', error.message);
}

// 测试 avatar.module 导入
try {
  console.log('测试 AvatarModule 导入...');
  const { AvatarModule } = require('./src/avatar/avatar.module');
  console.log('✅ AvatarModule 导入成功:', AvatarModule.name);
} catch (error) {
  console.error('❌ AvatarModule 导入失败:', error.message);
}

// 测试 avatar.entity 导入
try {
  console.log('测试 Avatar Entity 导入...');
  const { Avatar } = require('./src/avatar/entities/avatar.entity');
  console.log('✅ Avatar Entity 导入成功:', Avatar.name);
} catch (error) {
  console.error('❌ Avatar Entity 导入失败:', error.message);
}
