# 云边协同编排服务 (Cloud-Edge Orchestration Service)

DL引擎云边协同编排服务 - 混合云部署、边缘计算网络、5G网络应用管理

## 项目概述

云边协同编排服务是DL引擎的核心组件之一，专门负责管理和协调云端资源与边缘节点之间的工作负载分配、网络切片管理、资源优化等功能。

## 主要功能

### 🌐 云边协同编排
- 云资源与边缘节点统一管理
- 智能工作负载分配
- 动态资源调度

### 🔄 智能负载均衡
- 基于延迟优化的负载分配
- 实时性能监控
- 自动故障转移

### 📡 5G网络切片管理
- 动态网络切片创建
- 带宽和延迟保证
- 多租户隔离

### ⚡ 资源优化
- 成本优化算法
- 能耗管理
- 性能调优

### 📊 监控与分析
- 实时性能指标
- 健康状态检查
- 详细日志记录

## 技术架构

### 核心技术栈
- **框架**: NestJS 9.x
- **语言**: TypeScript 5.x
- **数据库**: MySQL 8.x
- **缓存**: Redis 7.x
- **容器**: Docker & Kubernetes
- **云平台**: AWS, Azure, Google Cloud

### 服务架构
```
┌─────────────────────────────────────────────────────────────┐
│                    云边协同编排服务                          │
├─────────────────────────────────────────────────────────────┤
│  API Gateway (NestJS)                                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 云边协同模块 │ │ 边缘节点模块 │ │ 工作负载模块 │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 网络切片模块 │ │ 资源优化模块 │ │ 监控模块    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  核心编排引擎                                               │
│  ┌─────────────────────┐ ┌─────────────────────┐           │
│  │ CloudEdgeOrchestrator│ │ EnhancedEdgeManager │           │
│  └─────────────────────┘ └─────────────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  数据层: MySQL + Redis                                     │
└─────────────────────────────────────────────────────────────┘
```

## 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0
- MySQL >= 8.0
- Redis >= 6.0

### 安装依赖
```bash
npm install
```

### 环境配置
复制环境配置文件并修改相应配置：
```bash
cp .env.example .env
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

### API文档
服务启动后访问：http://localhost:3003/api/docs

## 项目结构

```
src/
├── main.ts                 # 应用入口文件
├── app.module.ts           # 根模块
├── app.controller.ts       # 应用控制器
├── app.service.ts          # 应用服务
├── modules/                # 业务模块
│   ├── cloud-edge/         # 云边协同模块
│   ├── edge-node/          # 边缘节点管理
│   ├── workload/           # 工作负载管理
│   ├── network-slicing/    # 网络切片管理
│   ├── resource-optimization/ # 资源优化
│   ├── monitoring/         # 监控模块
│   └── health/             # 健康检查
└── orchestration/          # 核心编排引擎
    ├── cloud-edge-orchestrator.service.ts
    └── enhanced-edge-manager.service.ts
```

## API 接口

### 基础信息
- `GET /` - 获取服务基本信息
- `GET /health` - 健康检查
- `GET /version` - 版本信息

### 云边协同
- `GET /api/v1/cloud-edge/status` - 获取云边协同状态
- `POST /api/v1/cloud-edge/resources/cloud` - 注册云资源
- `POST /api/v1/cloud-edge/nodes/edge` - 注册边缘节点
- `POST /api/v1/cloud-edge/workloads/deploy` - 部署工作负载

### 边缘节点管理
- `GET /api/v1/edge-nodes` - 获取所有边缘节点
- `POST /api/v1/edge-nodes` - 创建边缘节点
- `GET /api/v1/edge-nodes/:nodeId` - 获取指定节点详情
- `PUT /api/v1/edge-nodes/:nodeId` - 更新节点配置
- `DELETE /api/v1/edge-nodes/:nodeId` - 删除节点

### 工作负载管理
- `GET /api/v1/workloads` - 获取所有工作负载
- `POST /api/v1/workloads` - 创建工作负载
- `GET /api/v1/workloads/:workloadId` - 获取工作负载详情
- `PUT /api/v1/workloads/:workloadId` - 更新工作负载
- `DELETE /api/v1/workloads/:workloadId` - 删除工作负载

## 开发指南

### 代码规范
- 使用 TypeScript 严格模式
- 遵循 ESLint 和 Prettier 配置
- 使用 NestJS 装饰器和依赖注入

### 测试
```bash
# 单元测试
npm run test

# 测试覆盖率
npm run test:cov

# E2E测试
npm run test:e2e
```

### 构建部署
```bash
# 构建
npm run build

# 生产启动
npm run start:prod
```

## 配置说明

### 环境变量
- `NODE_ENV` - 运行环境 (development/production)
- `PORT` - 服务端口 (默认: 3003)
- `DB_HOST` - 数据库主机
- `DB_PORT` - 数据库端口
- `REDIS_HOST` - Redis主机
- `REDIS_PORT` - Redis端口

### 功能开关
- `NETWORK_SLICE_ENABLED` - 启用网络切片功能
- `RESOURCE_OPTIMIZATION_ENABLED` - 启用资源优化
- `METRICS_ENABLED` - 启用指标收集

## 监控与运维

### 健康检查
- 基础健康检查: `GET /health`
- 详细健康检查: `GET /api/v1/health/detailed`

### 性能指标
- 系统指标: CPU、内存、网络使用率
- 业务指标: 节点数量、工作负载数量、响应时间
- 自定义指标: 资源利用率、成本优化效果

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。

## 联系方式

如有问题或建议，请联系开发团队。
