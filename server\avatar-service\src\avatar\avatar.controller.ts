/**
 * 虚拟化身控制器
 * 提供虚拟化身定制相关的API接口
 */
import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UploadedFile,
  UseInterceptors,
  // UseGuards,
  HttpStatus,
  HttpException
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes } from '@nestjs/swagger';
// import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AvatarService } from './avatar.service';
import { CreateAvatarDto } from './dto/create-avatar.dto';
import { UpdateAvatarDto } from './dto/update-avatar.dto';
import { BodyParametersDto } from './dto/body-parameters.dto';
import { ClothingItemDto } from './dto/clothing-item.dto';
import { Avatar } from './entities/avatar.entity';

@ApiTags('虚拟化身')
@Controller('avatar')
// @UseGuards(JwtAuthGuard)
// @ApiBearerAuth()
export class AvatarController {
  constructor(private readonly avatarService: AvatarService) {}

  /**
   * 创建虚拟化身
   */
  @Post()
  @ApiOperation({ summary: '创建虚拟化身' })
  @ApiResponse({ status: 201, description: '虚拟化身创建成功', type: Avatar })
  async create(@Body() createAvatarDto: CreateAvatarDto): Promise<Avatar> {
    try {
      return await this.avatarService.create(createAvatarDto);
    } catch (error) {
      throw new HttpException(
        `创建虚拟化身失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 上传用户照片
   */
  @Post(':id/upload-photo')
  @UseInterceptors(FileInterceptor('photo'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '上传用户照片' })
  @ApiResponse({ status: 200, description: '照片上传成功' })
  async uploadPhoto(
    @Param('id') id: string,
    @UploadedFile() file: any
  ): Promise<{ photoId: string; processedData: any }> {
    if (!file) {
      throw new HttpException('请上传照片文件', HttpStatus.BAD_REQUEST);
    }

    // 验证文件类型
    if (!file.mimetype.startsWith('image/')) {
      throw new HttpException('只支持图片文件', HttpStatus.BAD_REQUEST);
    }

    // 验证文件大小 (20MB)
    if (file.size > 20 * 1024 * 1024) {
      throw new HttpException('文件大小不能超过20MB', HttpStatus.BAD_REQUEST);
    }

    try {
      return await this.avatarService.uploadPhoto(id, file);
    } catch (error) {
      throw new HttpException(
        `照片上传失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 从照片重建面部
   */
  @Post(':id/reconstruct-face')
  @ApiOperation({ summary: '从照片重建面部' })
  @ApiResponse({ status: 200, description: '面部重建成功' })
  async reconstructFace(
    @Param('id') id: string,
    @Body() body: { photoId: string }
  ): Promise<{ faceData: any }> {
    try {
      return await this.avatarService.reconstructFace(id, body.photoId);
    } catch (error) {
      throw new HttpException(
        `面部重建失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 生成身体模型
   */
  @Post(':id/generate-body')
  @ApiOperation({ summary: '生成身体模型' })
  @ApiResponse({ status: 200, description: '身体模型生成成功' })
  async generateBody(
    @Param('id') id: string,
    @Body() bodyParams: BodyParametersDto
  ): Promise<{ bodyData: any }> {
    try {
      return await this.avatarService.generateBody(id, bodyParams);
    } catch (error) {
      throw new HttpException(
        `身体模型生成失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 应用服装
   */
  @Post(':id/apply-clothing')
  @ApiOperation({ summary: '应用服装' })
  @ApiResponse({ status: 200, description: '服装应用成功' })
  async applyClothing(
    @Param('id') id: string,
    @Body() body: { clothingItems: ClothingItemDto[] }
  ): Promise<{ clothingData: any }> {
    try {
      return await this.avatarService.applyClothing(id, body.clothingItems);
    } catch (error) {
      throw new HttpException(
        `服装应用失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 生成纹理
   */
  @Post(':id/generate-textures')
  @ApiOperation({ summary: '生成纹理' })
  @ApiResponse({ status: 200, description: '纹理生成成功' })
  async generateTextures(@Param('id') id: string): Promise<{ textureData: any }> {
    try {
      return await this.avatarService.generateTextures(id);
    } catch (error) {
      throw new HttpException(
        `纹理生成失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取虚拟化身列表
   */
  @Get()
  @ApiOperation({ summary: '获取虚拟化身列表' })
  @ApiResponse({ status: 200, description: '获取成功', type: [Avatar] })
  async findAll(
    @Query('userId') userId?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10
  ): Promise<{ avatars: Avatar[]; total: number; page: number; limit: number }> {
    try {
      return await this.avatarService.findAll({ userId, page, limit });
    } catch (error) {
      throw new HttpException(
        `获取虚拟化身列表失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取虚拟化身详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取虚拟化身详情' })
  @ApiResponse({ status: 200, description: '获取成功', type: Avatar })
  async findOne(@Param('id') id: string): Promise<Avatar> {
    try {
      const avatar = await this.avatarService.findOne(id);
      if (!avatar) {
        throw new HttpException('虚拟化身不存在', HttpStatus.NOT_FOUND);
      }
      return avatar;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `获取虚拟化身详情失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 更新虚拟化身
   */
  @Put(':id')
  @ApiOperation({ summary: '更新虚拟化身' })
  @ApiResponse({ status: 200, description: '更新成功', type: Avatar })
  async update(
    @Param('id') id: string,
    @Body() updateAvatarDto: UpdateAvatarDto
  ): Promise<Avatar> {
    try {
      return await this.avatarService.update(id, updateAvatarDto);
    } catch (error) {
      throw new HttpException(
        `更新虚拟化身失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 删除虚拟化身
   */
  @Delete(':id')
  @ApiOperation({ summary: '删除虚拟化身' })
  @ApiResponse({ status: 200, description: '删除成功' })
  async remove(@Param('id') id: string): Promise<{ success: boolean }> {
    try {
      await this.avatarService.remove(id);
      return { success: true };
    } catch (error) {
      throw new HttpException(
        `删除虚拟化身失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 导出虚拟化身模型
   */
  @Post(':id/export')
  @ApiOperation({ summary: '导出虚拟化身模型' })
  @ApiResponse({ status: 200, description: '导出成功' })
  async exportAvatar(
    @Param('id') id: string,
    @Body() body: { format: 'gltf' | 'fbx' | 'obj'; quality: 'low' | 'medium' | 'high' }
  ): Promise<{ downloadUrl: string; expiresAt: Date }> {
    try {
      return await this.avatarService.exportAvatar(id, body.format, body.quality);
    } catch (error) {
      throw new HttpException(
        `导出虚拟化身失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取处理状态
   */
  @Get(':id/status')
  @ApiOperation({ summary: '获取处理状态' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getProcessingStatus(@Param('id') id: string): Promise<{
    status: 'idle' | 'processing' | 'completed' | 'failed';
    progress: number;
    currentStep: string;
    error?: string;
  }> {
    try {
      return await this.avatarService.getProcessingStatus(id);
    } catch (error) {
      throw new HttpException(
        `获取处理状态失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取服装库
   */
  @Get('clothing/library')
  @ApiOperation({ summary: '获取服装库' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getClothingLibrary(
    @Query('category') category?: string,
    @Query('gender') gender?: 'male' | 'female',
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20
  ): Promise<{
    items: any[];
    total: number;
    categories: string[];
  }> {
    try {
      return await this.avatarService.getClothingLibrary({ category, gender, page, limit });
    } catch (error) {
      throw new HttpException(
        `获取服装库失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取发型库
   */
  @Get('hairstyles/library')
  @ApiOperation({ summary: '获取发型库' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getHairstyleLibrary(
    @Query('gender') gender?: 'male' | 'female',
    @Query('length') length?: 'short' | 'medium' | 'long',
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20
  ): Promise<{
    items: any[];
    total: number;
  }> {
    try {
      return await this.avatarService.getHairstyleLibrary({ gender, length, page, limit });
    } catch (error) {
      throw new HttpException(
        `获取发型库失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 预览虚拟化身
   */
  @Post(':id/preview')
  @ApiOperation({ summary: '生成预览图' })
  @ApiResponse({ status: 200, description: '预览生成成功' })
  async generatePreview(
    @Param('id') id: string,
    @Body() body: {
      angle: 'front' | 'side' | 'back';
      resolution: 'low' | 'medium' | 'high';
      background?: string;
    }
  ): Promise<{ previewUrl: string }> {
    try {
      return await this.avatarService.generatePreview(id, body);
    } catch (error) {
      throw new HttpException(
        `生成预览失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
