/**
 * 资产控制器
 */

import { Controller, Get, Post, Put, Param, Body, Query } from '@nestjs/common';
import { AssetService } from './asset.service';
import { BlockchainAsset } from '../../entities/blockchain-asset.entity';

@Controller('assets')
export class AssetController {
  constructor(private readonly assetService: AssetService) {}

  @Post()
  async createAsset(@Body() assetData: Partial<BlockchainAsset>) {
    return this.assetService.createAsset(assetData);
  }

  @Get(':id')
  async getAsset(@Param('id') id: string) {
    return this.assetService.getAssetById(id);
  }

  @Get('token/:contractAddress/:tokenId')
  async getAssetByToken(
    @Param('contractAddress') contractAddress: string,
    @Param('tokenId') tokenId: string
  ) {
    return this.assetService.getAssetByToken(contractAddress, tokenId);
  }

  @Get('owner/:ownerId')
  async getAssetsByOwner(@Param('ownerId') ownerId: string) {
    return this.assetService.getAssetsByOwner(ownerId);
  }

  @Put(':id')
  async updateAsset(
    @Param('id') id: string,
    @Body() updateData: Partial<BlockchainAsset>
  ) {
    return this.assetService.updateAsset(id, updateData);
  }
}
