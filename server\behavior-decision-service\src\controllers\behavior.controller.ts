/**
 * 行为决策控制器
 */

import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  HttpException,
  HttpStatus,
  Logger
} from '@nestjs/common';
import { DistributedBehaviorService, DistributedDecisionRequest, CoordinationStrategy } from '../services/distributed-behavior.service';

/**
 * 决策请求DTO
 */
export class DecisionRequestDto {
  entityId: string;
  sessionId: string;
  context: any;
  options: any[];
  strategy?: string;
  priority: number = 2;
  timeout: number = 30000;
}

/**
 * 行为决策控制器
 */
@Controller('behavior')
export class BehaviorController {
  private readonly logger = new Logger(BehaviorController.name);

  constructor(
    private readonly behaviorService: DistributedBehaviorService
  ) {}

  /**
   * 提交决策请求
   */
  @Post('decision')
  async makeDecision(@Body() requestDto: DecisionRequestDto) {
    try {
      const request: DistributedDecisionRequest = {
        requestId: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        entityId: requestDto.entityId,
        sessionId: requestDto.sessionId,
        context: requestDto.context,
        options: requestDto.options,
        strategy: requestDto.strategy,
        priority: requestDto.priority,
        timeout: requestDto.timeout,
        timestamp: Date.now()
      };

      const response = await this.behaviorService.makeDecision(request);
      
      return {
        success: true,
        data: response,
        message: '决策完成'
      };

    } catch (error) {
      this.logger.error('决策请求失败:', error);
      throw new HttpException(
        {
          success: false,
          message: error.message || '决策请求失败',
          error: error.name
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取节点状态
   */
  @Get('status')
  async getNodeStatus() {
    try {
      const status = this.behaviorService.getNodeStatus();
      
      return {
        success: true,
        data: status,
        message: '获取节点状态成功'
      };

    } catch (error) {
      this.logger.error('获取节点状态失败:', error);
      throw new HttpException(
        {
          success: false,
          message: '获取节点状态失败',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取所有节点状态
   */
  @Get('nodes')
  async getAllNodeStatuses() {
    try {
      const statuses = await this.behaviorService.getAllNodeStatuses();
      
      return {
        success: true,
        data: statuses,
        message: '获取所有节点状态成功'
      };

    } catch (error) {
      this.logger.error('获取所有节点状态失败:', error);
      throw new HttpException(
        {
          success: false,
          message: '获取所有节点状态失败',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 设置协调策略
   */
  @Post('strategy/:strategy')
  async setCoordinationStrategy(@Param('strategy') strategy: string) {
    try {
      if (!Object.values(CoordinationStrategy).includes(strategy as CoordinationStrategy)) {
        throw new HttpException('无效的协调策略', HttpStatus.BAD_REQUEST);
      }

      await this.behaviorService.setCoordinationStrategy(strategy as CoordinationStrategy);
      
      return {
        success: true,
        message: `协调策略已设置为: ${strategy}`
      };

    } catch (error) {
      this.logger.error('设置协调策略失败:', error);
      throw new HttpException(
        {
          success: false,
          message: '设置协调策略失败',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 健康检查
   */
  @Get('health')
  async healthCheck() {
    return {
      success: true,
      message: '服务运行正常',
      timestamp: new Date().toISOString()
    };
  }
}
