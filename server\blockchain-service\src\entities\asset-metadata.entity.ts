/**
 * 资产元数据实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { BlockchainAsset } from './blockchain-asset.entity';

@Entity('asset_metadata')
@Index(['assetId'])
@Index(['metadataType'])
export class AssetMetadata {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'enum', enum: ['original', 'thumbnail', 'preview', 'animation', 'document', 'other'], nullable: false })
  metadataType: string;

  @Column({ type: 'varchar', length: 500, nullable: false })
  url: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  mimeType: string;

  @Column({ type: 'bigint', nullable: true })
  fileSize: number;

  @Column({ type: 'int', nullable: true })
  width: number;

  @Column({ type: 'int', nullable: true })
  height: number;

  @Column({ type: 'int', nullable: true })
  duration: number;

  @Column({ type: 'varchar', length: 64, nullable: true })
  hash: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  ipfsHash: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  ipfsUrl: string;

  @Column({ type: 'boolean', default: false })
  isPinned: boolean;

  @Column({ type: 'json', nullable: true })
  properties: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  description: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => BlockchainAsset, asset => asset.metadata)
  @JoinColumn({ name: 'assetId' })
  asset: BlockchainAsset;

  @Column({ type: 'uuid' })
  assetId: string;
}
