/**
 * IPFS控制器
 */

import { Controller, Get, Post, Param, Body, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { IPFSService } from './ipfs.service';

@Controller('ipfs')
export class IPFSController {
  constructor(private readonly ipfsService: IPFSService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    return this.ipfsService.uploadFile(file.buffer, file.originalname);
  }

  @Post('metadata')
  async uploadMetadata(@Body() metadata: Record<string, any>) {
    return this.ipfsService.uploadMetadata(metadata);
  }

  @Get(':hash')
  async getFile(@Param('hash') hash: string) {
    return this.ipfsService.getFile(hash);
  }

  @Post(':hash/pin')
  async pinFile(@Param('hash') hash: string) {
    return this.ipfsService.pinFile(hash);
  }
}
