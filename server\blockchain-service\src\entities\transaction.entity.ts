/**
 * 交易实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { BlockchainAsset } from './blockchain-asset.entity';
import { SmartContract } from './smart-contract.entity';

@Entity('transactions')
@Index(['hash'], { unique: true })
@Index(['fromUserId'])
@Index(['toUserId'])
@Index(['assetId'])
@Index(['contractId'])
@Index(['blockchain'])
@Index(['status'])
export class Transaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 66, unique: true, nullable: false })
  hash: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  blockchain: string;

  @Column({ type: 'int', nullable: false })
  chainId: number;

  @Column({ type: 'bigint', nullable: false })
  blockNumber: number;

  @Column({ type: 'int', nullable: false })
  transactionIndex: number;

  @Column({ type: 'varchar', length: 42, nullable: false })
  from: string;

  @Column({ type: 'varchar', length: 42, nullable: false })
  to: string;

  @Column({ type: 'decimal', precision: 36, scale: 18, nullable: false })
  value: string;

  @Column({ type: 'bigint', nullable: false })
  gasLimit: number;

  @Column({ type: 'bigint', nullable: false })
  gasUsed: number;

  @Column({ type: 'decimal', precision: 36, scale: 18, nullable: false })
  gasPrice: string;

  @Column({ type: 'decimal', precision: 36, scale: 18, nullable: false })
  transactionFee: string;

  @Column({ type: 'enum', enum: ['transfer', 'mint', 'burn', 'approve', 'list', 'buy', 'cancel', 'bid', 'other'], nullable: false })
  type: string;

  @Column({ type: 'enum', enum: ['pending', 'confirmed', 'failed', 'dropped'], default: 'pending' })
  status: string;

  @Column({ type: 'int', default: 0 })
  confirmations: number;

  @Column({ type: 'text', nullable: true })
  input: string;

  @Column({ type: 'text', nullable: true })
  logs: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  errorMessage: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'timestamp', nullable: false })
  timestamp: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, user => user.sentTransactions, { nullable: true })
  @JoinColumn({ name: 'fromUserId' })
  fromUser: User;

  @Column({ type: 'uuid', nullable: true })
  fromUserId: string;

  @ManyToOne(() => User, user => user.receivedTransactions, { nullable: true })
  @JoinColumn({ name: 'toUserId' })
  toUser: User;

  @Column({ type: 'uuid', nullable: true })
  toUserId: string;

  @ManyToOne(() => BlockchainAsset, asset => asset.transactions, { nullable: true })
  @JoinColumn({ name: 'assetId' })
  asset: BlockchainAsset;

  @Column({ type: 'uuid', nullable: true })
  assetId: string;

  @ManyToOne(() => SmartContract, contract => contract.transactions, { nullable: true })
  @JoinColumn({ name: 'contractId' })
  contract: SmartContract;

  @Column({ type: 'uuid', nullable: true })
  contractId: string;
}
