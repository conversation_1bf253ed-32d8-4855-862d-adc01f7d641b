/**
 * 项目结构验证脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始验证云边协同编排服务项目结构...\n');

// 必需的文件和目录
const requiredStructure = [
  // 根文件
  'package.json',
  'tsconfig.json',
  'nest-cli.json',
  '.env',
  'README.md',
  
  // 源码目录
  'src/main.ts',
  'src/app.module.ts',
  'src/app.controller.ts',
  'src/app.service.ts',
  
  // 模块目录
  'src/modules/cloud-edge/cloud-edge.module.ts',
  'src/modules/cloud-edge/cloud-edge.controller.ts',
  'src/modules/edge-node/edge-node.module.ts',
  'src/modules/edge-node/edge-node.controller.ts',
  'src/modules/edge-node/edge-node.service.ts',
  'src/modules/workload/workload.module.ts',
  'src/modules/workload/workload.controller.ts',
  'src/modules/workload/workload.service.ts',
  'src/modules/health/health.module.ts',
  'src/modules/health/health.controller.ts',
  'src/modules/health/health.service.ts',
  
  // 核心编排引擎
  'src/orchestration/cloud-edge-orchestrator.service.ts',
  'src/orchestration/enhanced-edge-manager.service.ts',
  
  // 构建输出
  'dist/main.js',
  'dist/app.module.js',
];

let allValid = true;
let validCount = 0;
let totalCount = requiredStructure.length;

console.log('📁 检查文件和目录结构:');
console.log('─'.repeat(50));

requiredStructure.forEach(filePath => {
  const fullPath = path.join(__dirname, '..', filePath);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    console.log(`✅ ${filePath}`);
    validCount++;
  } else {
    console.log(`❌ ${filePath} - 文件不存在`);
    allValid = false;
  }
});

console.log('\n' + '─'.repeat(50));
console.log(`📊 验证结果: ${validCount}/${totalCount} 文件存在`);

// 检查package.json内容
console.log('\n📦 检查 package.json 配置:');
console.log('─'.repeat(50));

try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  
  const requiredScripts = ['build', 'start', 'start:dev', 'start:prod', 'test'];
  const requiredDeps = ['@nestjs/core', '@nestjs/common', '@nestjs/platform-express'];
  
  console.log('🔧 Scripts:');
  requiredScripts.forEach(script => {
    if (packageJson.scripts && packageJson.scripts[script]) {
      console.log(`  ✅ ${script}: ${packageJson.scripts[script]}`);
    } else {
      console.log(`  ❌ ${script} - 脚本缺失`);
      allValid = false;
    }
  });
  
  console.log('\n📚 核心依赖:');
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`  ✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`  ❌ ${dep} - 依赖缺失`);
      allValid = false;
    }
  });
  
} catch (error) {
  console.log(`❌ 无法读取 package.json: ${error.message}`);
  allValid = false;
}

// 检查TypeScript配置
console.log('\n⚙️ 检查 TypeScript 配置:');
console.log('─'.repeat(50));

try {
  const tsConfig = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'tsconfig.json'), 'utf8'));
  
  const requiredOptions = ['experimentalDecorators', 'emitDecoratorMetadata', 'target', 'module'];
  
  requiredOptions.forEach(option => {
    if (tsConfig.compilerOptions && tsConfig.compilerOptions[option] !== undefined) {
      console.log(`✅ ${option}: ${tsConfig.compilerOptions[option]}`);
    } else {
      console.log(`❌ ${option} - 配置缺失`);
      allValid = false;
    }
  });
  
} catch (error) {
  console.log(`❌ 无法读取 tsconfig.json: ${error.message}`);
  allValid = false;
}

// 检查环境配置
console.log('\n🌍 检查环境配置:');
console.log('─'.repeat(50));

try {
  const envContent = fs.readFileSync(path.join(__dirname, '..', '.env'), 'utf8');
  const envLines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
  
  const requiredEnvVars = ['NODE_ENV', 'PORT', 'DB_HOST', 'REDIS_HOST'];
  
  requiredEnvVars.forEach(envVar => {
    const found = envLines.some(line => line.startsWith(`${envVar}=`));
    if (found) {
      console.log(`✅ ${envVar} - 已配置`);
    } else {
      console.log(`⚠️ ${envVar} - 未配置`);
    }
  });
  
} catch (error) {
  console.log(`❌ 无法读取 .env 文件: ${error.message}`);
}

// 最终结果
console.log('\n' + '='.repeat(50));
if (allValid) {
  console.log('🎉 项目结构验证通过！');
  console.log('✨ 云边协同编排服务项目已完整配置');
  console.log('\n📋 下一步操作:');
  console.log('   1. npm install - 安装依赖');
  console.log('   2. npm run build - 构建项目');
  console.log('   3. npm run start:dev - 启动开发服务器');
  console.log('   4. 访问 http://localhost:3003/api/docs 查看API文档');
} else {
  console.log('⚠️ 项目结构验证失败！');
  console.log('请检查上述缺失的文件和配置');
  process.exit(1);
}

console.log('='.repeat(50));
