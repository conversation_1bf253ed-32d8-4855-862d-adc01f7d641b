/**
 * 智慧工厂高级分析和报表服务主入口
 */
import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AnalyticsModule } from './analytics.module';

async function bootstrap() {
  const logger = new Logger('AnalyticsService');

  try {
    // 创建应用实例
    const app = await NestFactory.create(AnalyticsModule);
    
    // 获取配置服务
    const configService = app.get(ConfigService);
    
    // 设置全局前缀
    app.setGlobalPrefix('api/v1');

    // 启用CORS
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN', '*'),
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID'],
      credentials: true
    });

    // 全局管道
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true
      }
    }));

    // Swagger 文档配置
    const config = new DocumentBuilder()
      .setTitle('智慧工厂分析服务 API')
      .setDescription('提供高级分析、报表生成和数据可视化服务')
      .setVersion('1.0')
      .addTag('analytics', '分析服务')
      .addTag('reports', '报表服务')
      .addTag('visualization', '数据可视化')
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document);

    // 启动HTTP服务
    const port = configService.get<number>('PORT', 3007);
    const host = configService.get<string>('HOST', '0.0.0.0');

    await app.listen(port, host);

    logger.log(`智慧工厂分析服务已启动`);
    logger.log(`HTTP服务: http://${host}:${port}`);
    logger.log(`API文档: http://${host}:${port}/api/docs`);
    logger.log(`环境: ${configService.get<string>('NODE_ENV', 'development')}`);

    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('收到SIGTERM信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('收到SIGINT信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('智慧工厂分析服务启动失败:', error);
    process.exit(1);
  }
}

// 启动应用
bootstrap().catch(error => {
  console.error('应用启动失败:', error);
  process.exit(1);
});
