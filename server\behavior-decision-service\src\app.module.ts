/**
 * 分布式行为决策服务应用模块
 */

import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { DistributedBehaviorService } from './services/distributed-behavior.service';
import { BehaviorController } from './controllers/behavior.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
  ],
  controllers: [BehaviorController],
  providers: [
    DistributedBehaviorService,
    {
      provide: 'REDIS_CONFIG',
      useFactory: () => ({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0'),
      }),
    },
  ],
  exports: [DistributedBehaviorService],
})
export class AppModule {}
