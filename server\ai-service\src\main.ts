/**
 * AI服务主入口
 * 提供AI算法管理和训练服务
 */
import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { AIServiceModule } from './ai-service.module';

async function bootstrap() {
  const logger = new Logger('AIService');

  try {
    // 创建应用实例
    const app = await NestFactory.create(AIServiceModule);
    
    // 获取配置服务
    const configService = app.get(ConfigService);
    
    // 设置全局前缀
    app.setGlobalPrefix('api/v1');

    // 启用CORS
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN', '*'),
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID'],
      credentials: true
    });

    // 全局管道
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true
      }
    }));

    // 配置微服务
    const microserviceOptions: MicroserviceOptions = {
      transport: Transport.TCP,
      options: {
        host: configService.get<string>('MICROSERVICE_HOST', 'localhost'),
        port: configService.get<number>('MICROSERVICE_PORT', 3019),
        retryAttempts: 5,
        retryDelay: 3000
      }
    };

    // 连接微服务
    app.connectMicroservice(microserviceOptions);
    await app.startAllMicroservices();

    // 启动HTTP服务
    const port = configService.get<number>('PORT', 3009);
    const host = configService.get<string>('HOST', '0.0.0.0');

    await app.listen(port, host);

    logger.log(`AI服务已启动`);
    logger.log(`HTTP服务: http://${host}:${port}`);
    logger.log(`微服务: tcp://${microserviceOptions.options.host}:${microserviceOptions.options.port}`);
    logger.log(`环境: ${configService.get<string>('NODE_ENV', 'development')}`);

    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('收到SIGTERM信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('收到SIGINT信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('AI服务启动失败:', error);
    process.exit(1);
  }
}

// 启动应用
bootstrap().catch(error => {
  console.error('应用启动失败:', error);
  process.exit(1);
});
