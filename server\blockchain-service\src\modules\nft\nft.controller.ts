/**
 * NFT控制器
 */

import { Controller, Get, Post, Put, Param, Body, Query } from '@nestjs/common';
import { NFTService } from './nft.service';
import { NFTToken } from '../../entities/nft-token.entity';

@Controller('nft')
export class NFTController {
  constructor(private readonly nftService: NFTService) {}

  @Post()
  async createNFT(@Body() nftData: Partial<NFTToken>) {
    return this.nftService.createNFT(nftData);
  }

  @Get(':id')
  async getNFT(@Param('id') id: string) {
    return this.nftService.getNFTById(id);
  }

  @Get('token/:contractAddress/:tokenId')
  async getNFTByToken(
    @Param('contractAddress') contractAddress: string,
    @Param('tokenId') tokenId: string
  ) {
    return this.nftService.getNFTByToken(contractAddress, tokenId);
  }

  @Get('owner/:ownerId')
  async getNFTsByOwner(@Param('ownerId') ownerId: string) {
    return this.nftService.getNFTsByOwner(ownerId);
  }

  @Put(':id')
  async updateNFT(
    @Param('id') id: string,
    @Body() updateData: Partial<NFTToken>
  ) {
    return this.nftService.updateNFT(id, updateData);
  }

  @Post(':id/mint')
  async mintNFT(@Param('id') id: string, @Body() mintData: any) {
    return this.nftService.mintNFT(id, mintData);
  }

  @Post(':id/transfer')
  async transferNFT(
    @Param('id') id: string,
    @Body() transferData: { to: string; amount?: number }
  ) {
    return this.nftService.transferNFT(id, transferData.to, transferData.amount);
  }
}
