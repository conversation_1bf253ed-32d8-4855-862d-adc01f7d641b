/**
 * 协作模块
 */
import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { CollaborationGateway } from './collaboration.gateway';
import { CollaborationService } from './collaboration.service';
import { EnhancedCollaborationService } from './enhanced-collaboration.service';
import { RealtimeSyncService } from './realtime-sync.service';
import { AuthService } from './auth/auth.service';
import { ProjectService } from './project/project.service';

@Module({
  imports: [
    // JWT模块
    JwtModule.registerAsync({
      useFactory: () => ({
        secret: process.env.JWT_SECRET || 'dl-engine-secret',
        signOptions: { expiresIn: '1d' },
      }),
    }),

    // 微服务客户端模块
    ClientsModule.register([
      {
        name: 'USER_SERVICE',
        transport: Transport.TCP,
        options: {
          host: process.env.USER_SERVICE_HOST || 'localhost',
          port: parseInt(process.env.USER_SERVICE_PORT || '3002'),
        },
      },
      {
        name: 'PROJECT_SERVICE',
        transport: Transport.TCP,
        options: {
          host: process.env.PROJECT_SERVICE_HOST || 'localhost',
          port: parseInt(process.env.PROJECT_SERVICE_PORT || '3003'),
        },
      },
    ]),
  ],
  providers: [
    CollaborationGateway,
    CollaborationService,
    EnhancedCollaborationService,
    RealtimeSyncService,
    AuthService,
    ProjectService,
  ],
  exports: [
    CollaborationGateway,
    CollaborationService,
  ],
})
export class CollaborationModule {}
