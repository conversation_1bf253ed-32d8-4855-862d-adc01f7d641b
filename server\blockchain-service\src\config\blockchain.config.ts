/**
 * 区块链配置
 */

import { registerAs } from '@nestjs/config';

export default registerAs('blockchain', () => ({
  // 以太坊网络配置
  ethereum: {
    rpcUrl: process.env.ETHEREUM_RPC_URL || 'https://mainnet.infura.io/v3/your-project-id',
    testnetRpcUrl: process.env.ETHEREUM_TESTNET_RPC_URL || 'https://goerli.infura.io/v3/your-project-id',
    chainId: parseInt(process.env.ETHEREUM_CHAIN_ID, 10) || 1,
    testnetChainId: parseInt(process.env.ETHEREUM_TESTNET_CHAIN_ID, 10) || 5,
    gasLimit: parseInt(process.env.ETHEREUM_GAS_LIMIT, 10) || 21000,
    gasPrice: process.env.ETHEREUM_GAS_PRICE || '20000000000', // 20 Gwei
  },

  // BSC网络配置
  bsc: {
    rpcUrl: process.env.BSC_RPC_URL || 'https://bsc-dataseed1.binance.org/',
    testnetRpcUrl: process.env.BSC_TESTNET_RPC_URL || 'https://data-seed-prebsc-1-s1.binance.org:8545/',
    chainId: parseInt(process.env.BSC_CHAIN_ID, 10) || 56,
    testnetChainId: parseInt(process.env.BSC_TESTNET_CHAIN_ID, 10) || 97,
    gasLimit: parseInt(process.env.BSC_GAS_LIMIT, 10) || 21000,
    gasPrice: process.env.BSC_GAS_PRICE || '5000000000', // 5 Gwei
  },

  // Polygon网络配置
  polygon: {
    rpcUrl: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com/',
    testnetRpcUrl: process.env.POLYGON_TESTNET_RPC_URL || 'https://rpc-mumbai.maticvigil.com/',
    chainId: parseInt(process.env.POLYGON_CHAIN_ID, 10) || 137,
    testnetChainId: parseInt(process.env.POLYGON_TESTNET_CHAIN_ID, 10) || 80001,
    gasLimit: parseInt(process.env.POLYGON_GAS_LIMIT, 10) || 21000,
    gasPrice: process.env.POLYGON_GAS_PRICE || '30000000000', // 30 Gwei
  },

  // 钱包配置
  wallet: {
    privateKey: process.env.WALLET_PRIVATE_KEY || '',
    mnemonic: process.env.WALLET_MNEMONIC || '',
  },

  // IPFS配置
  ipfs: {
    gateway: process.env.IPFS_GATEWAY || 'https://ipfs.io/ipfs/',
    apiUrl: process.env.IPFS_API_URL || 'https://api.pinata.cloud',
    apiKey: process.env.IPFS_API_KEY || '',
    secretKey: process.env.IPFS_SECRET_KEY || '',
  },

  // 合约地址配置
  contracts: {
    nftMarketplace: process.env.NFT_MARKETPLACE_CONTRACT || '',
    erc721Factory: process.env.ERC721_FACTORY_CONTRACT || '',
    erc1155Factory: process.env.ERC1155_FACTORY_CONTRACT || '',
    paymentToken: process.env.PAYMENT_TOKEN_CONTRACT || '',
  },

  // 网络环境
  network: process.env.BLOCKCHAIN_NETWORK || 'testnet', // mainnet | testnet
  
  // 交易配置
  transaction: {
    confirmations: parseInt(process.env.TX_CONFIRMATIONS, 10) || 3,
    timeout: parseInt(process.env.TX_TIMEOUT, 10) || 300000, // 5分钟
    retryAttempts: parseInt(process.env.TX_RETRY_ATTEMPTS, 10) || 3,
    retryDelay: parseInt(process.env.TX_RETRY_DELAY, 10) || 5000, // 5秒
  },
}));
