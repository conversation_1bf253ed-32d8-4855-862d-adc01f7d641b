/**
 * 验证修复结果
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证分布式行为决策服务修复结果...\n');

// 检查 main.ts 中的导入
try {
  const mainContent = fs.readFileSync(path.join(__dirname, 'src/main.ts'), 'utf8');
  
  if (mainContent.includes("from './app.module'")) {
    console.log('✅ main.ts - app.module 导入路径正确');
  } else {
    console.log('❌ main.ts - app.module 导入路径有问题');
  }
  
} catch (error) {
  console.log('❌ 无法读取 main.ts:', error.message);
}

// 检查 app.module.ts 是否存在且格式正确
try {
  const appModuleContent = fs.readFileSync(path.join(__dirname, 'src/app.module.ts'), 'utf8');
  
  if (appModuleContent.includes('export class AppModule')) {
    console.log('✅ app.module.ts - 模块导出正确');
  } else {
    console.log('❌ app.module.ts - 模块导出有问题');
  }
  
} catch (error) {
  console.log('❌ 无法读取 app.module.ts:', error.message);
}

// 检查 tsconfig.json 配置
try {
  const tsconfigContent = fs.readFileSync(path.join(__dirname, 'tsconfig.json'), 'utf8');
  const tsconfig = JSON.parse(tsconfigContent);
  
  if (tsconfig.compilerOptions.moduleResolution === 'node') {
    console.log('✅ tsconfig.json - 模块解析配置正确');
  } else {
    console.log('⚠️  tsconfig.json - 缺少模块解析配置');
  }
  
  if (tsconfig.compilerOptions.esModuleInterop === true) {
    console.log('✅ tsconfig.json - ES模块互操作配置正确');
  } else {
    console.log('⚠️  tsconfig.json - 缺少ES模块互操作配置');
  }
  
} catch (error) {
  console.log('❌ 无法读取或解析 tsconfig.json:', error.message);
}

// 检查控制器中的已弃用方法
try {
  const controllerContent = fs.readFileSync(path.join(__dirname, 'src/controllers/behavior.controller.ts'), 'utf8');
  
  if (controllerContent.includes('.substring(')) {
    console.log('✅ behavior.controller.ts - 已修复弃用的 substr 方法');
  } else if (controllerContent.includes('.substr(')) {
    console.log('⚠️  behavior.controller.ts - 仍在使用已弃用的 substr 方法');
  } else {
    console.log('✅ behavior.controller.ts - 字符串方法正常');
  }
  
} catch (error) {
  console.log('❌ 无法读取 behavior.controller.ts:', error.message);
}

console.log('\n🎉 修复验证完成！');
console.log('📝 主要修复内容:');
console.log('   - 修复了 app.module 导入问题');
console.log('   - 优化了 TypeScript 配置');
console.log('   - 修复了已弃用的方法调用');
console.log('   - 清理了未使用的导入');
