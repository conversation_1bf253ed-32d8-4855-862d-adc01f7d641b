/**
 * 虚拟化身服务启动文件
 */
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 启用全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));

  // 启用CORS
  app.enableCors({
    origin: process.env.CORS_ORIGIN || '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
  });

  // 设置API文档
  const config = new DocumentBuilder()
    .setTitle('DL引擎虚拟化身服务')
    .setDescription('提供虚拟化身创建、定制和管理功能')
    .setVersion('1.0')
    .addTag('avatar', '虚拟化身管理')
    .addTag('upload', '文件上传')
    .addTag('scene', '场景管理')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  const port = process.env.PORT || 3005;
  await app.listen(port);
  
  console.log(`虚拟化身服务已启动，端口: ${port}`);
  console.log(`API文档地址: http://localhost:${port}/api`);
}

bootstrap();
